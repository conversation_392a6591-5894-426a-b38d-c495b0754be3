//+------------------------------------------------------------------+
//|                                               SignalFileManager.mqh |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include "SignalProcessor.mqh"

//+------------------------------------------------------------------+
//| Signal File Manager - Handles file-based signal communication    |
//+------------------------------------------------------------------+

// File paths for each satellite bot
#define SIGNAL_FILE_FAIR_FOREX "signals_fair_forex.dat"
#define SIGNAL_FILE_ORDER_BLOCK "signals_order_block.dat"
#define SIGNAL_FILE_MARKET_STRUCTURE "signals_market_structure.dat"
#define SIGNAL_FILE_RANGE_BREAKOUT "signals_range_breakout.dat"
#define SIGNAL_FILE_SUPPORT_RESISTANCE "signals_support_resistance.dat"

// Signal file structure - using char arrays instead of strings for binary compatibility
struct SignalFileHeader {
    datetime timestamp;      // When the signal was written
    char bot_name[64];      // Name of the bot that wrote the signal (fixed size)
    int signal_version;     // Version of the signal format
    bool is_valid;          // Whether this signal is still valid
};

//+------------------------------------------------------------------+
//| Write signal to file (for satellite bots)                       |
//+------------------------------------------------------------------+
bool WriteSignalToFile(string filename, TradingSignal &signal, string botName) {
    int fileHandle = FileOpen(filename, FILE_WRITE | FILE_BIN);
    if(fileHandle == INVALID_HANDLE) {
        Print("Failed to open signal file for writing: ", filename, " Error: ", GetLastError());
        return false;
    }
    
    // Write header
    SignalFileHeader header;
    header.timestamp = TimeCurrent();
    StringToCharArray(botName, header.bot_name, 0, 63); // Copy string to char array
    header.signal_version = 1;
    header.is_valid = true;
    
    if(FileWriteStruct(fileHandle, header) <= 0) {
        Print("Failed to write signal header to file: ", filename);
        FileClose(fileHandle);
        return false;
    }
    
    // Write signal data
    if(FileWriteStruct(fileHandle, signal) <= 0) {
        Print("Failed to write signal data to file: ", filename);
        FileClose(fileHandle);
        return false;
    }
    
    FileClose(fileHandle);
    Print("Signal written to file: ", filename, " by ", botName);
    return true;
}

//+------------------------------------------------------------------+
//| Read signal from file (for Misape Bot)                          |
//+------------------------------------------------------------------+
bool ReadSignalFromFile(string filename, TradingSignal &signal, SignalFileHeader &header) {
    int fileHandle = FileOpen(filename, FILE_READ | FILE_BIN);
    if(fileHandle == INVALID_HANDLE) {
        // File doesn't exist or can't be opened
        return false;
    }
    
    // Read header
    if(FileReadStruct(fileHandle, header) <= 0) {
        Print("Failed to read signal header from file: ", filename);
        FileClose(fileHandle);
        return false;
    }
    
    // Check if signal is still valid (not older than 5 minutes)
    if(TimeCurrent() - header.timestamp > 300) { // 5 minutes = 300 seconds
        Print("Signal in file ", filename, " is too old, timestamp: ", TimeToString(header.timestamp));
        FileClose(fileHandle);
        return false;
    }
    
    // Read signal data
    if(FileReadStruct(fileHandle, signal) <= 0) {
        Print("Failed to read signal data from file: ", filename);
        FileClose(fileHandle);
        return false;
    }
    
    FileClose(fileHandle);
    return true;
}

//+------------------------------------------------------------------+
//| Get signal file path for a specific bot                         |
//+------------------------------------------------------------------+
string GetSignalFilePath(string botName) {
    if(StringFind(botName, "Fair forex look") >= 0) {
        return SIGNAL_FILE_FAIR_FOREX;
    } else if(StringFind(botName, "Advanced Order Block") >= 0) {
        return SIGNAL_FILE_ORDER_BLOCK;
    } else if(StringFind(botName, "Market Structure") >= 0) {
        return SIGNAL_FILE_MARKET_STRUCTURE;
    } else if(StringFind(botName, "Range Breakout") >= 0) {
        return SIGNAL_FILE_RANGE_BREAKOUT;
    } else if(StringFind(botName, "Support and Resistance") >= 0) {
        return SIGNAL_FILE_SUPPORT_RESISTANCE;
    }
    return "signals_unknown.dat";
}

//+------------------------------------------------------------------+
//| Clean up old signal files                                        |
//+------------------------------------------------------------------+
void CleanupOldSignalFiles() {
    string files[] = {SIGNAL_FILE_FAIR_FOREX, SIGNAL_FILE_ORDER_BLOCK, 
                     SIGNAL_FILE_MARKET_STRUCTURE, SIGNAL_FILE_RANGE_BREAKOUT, 
                     SIGNAL_FILE_SUPPORT_RESISTANCE};
    
    for(int i = 0; i < ArraySize(files); i++) {
        if(FileIsExist(files[i])) {
            int fileHandle = FileOpen(files[i], FILE_READ | FILE_BIN);
            if(fileHandle != INVALID_HANDLE) {
                SignalFileHeader header;
                if(FileReadStruct(fileHandle, header) > 0) {
                    // Check if file is older than 10 minutes
                    if(TimeCurrent() - header.timestamp > 600) { // 10 minutes
                        FileClose(fileHandle);
                        FileDelete(files[i]);
                        Print("Deleted old signal file: ", files[i]);
                    }
                }
                FileClose(fileHandle);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check if signal file exists and is recent                       |
//+------------------------------------------------------------------+
bool IsSignalFileValid(string filename) {
    if(!FileIsExist(filename)) {
        return false;
    }
    
    int fileHandle = FileOpen(filename, FILE_READ | FILE_BIN);
    if(fileHandle == INVALID_HANDLE) {
        return false;
    }
    
    SignalFileHeader header;
    bool isValid = false;
    
    if(FileReadStruct(fileHandle, header) > 0) {
        // Check if signal is not older than 5 minutes
        isValid = (TimeCurrent() - header.timestamp <= 300);
    }
    
    FileClose(fileHandle);
    return isValid;
}

//+------------------------------------------------------------------+
//| Get bot name from header (convert char array to string)         |
//+------------------------------------------------------------------+
string GetBotNameFromHeader(SignalFileHeader &header) {
    return CharArrayToString(header.bot_name);
} 