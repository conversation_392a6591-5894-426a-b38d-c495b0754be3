//+------------------------------------------------------------------+
//|                                               SignalProcessor.mqh |
//|                                  Copyright 2025, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade/Trade.mqh>

//+------------------------------------------------------------------+
//| Trading Signal Type Enum                                         |
//+------------------------------------------------------------------+
enum ENUM_SIGNAL_TYPE {
    SIGNAL_TYPE_HOLD = 0,  // No signal
    SIGNAL_TYPE_BUY = 1,   // Buy signal
    SIGNAL_TYPE_SELL = 2   // Sell signal
};

//+------------------------------------------------------------------+
//| Trading Signal Structure                                         |
//+------------------------------------------------------------------+
struct TradingSignal {
    ENUM_SIGNAL_TYPE signal_type;    // Type of signal (HOLD, BUY, SELL)
    double confidence_level;          // Confidence level (0.0 to 1.0)
    double stop_loss;                 // Stop loss price
    double take_profit;               // Take profit price
    char parameters[256];             // Additional parameters (JSON string)
    char strategy_name[64];           // Name of the strategy that generated the signal
};

//+------------------------------------------------------------------+
//| Signal Processor Class                                           |
//+------------------------------------------------------------------+
class CSignalProcessor {
private:
    CTrade m_trade;                 // Trade object
    
public:
    CSignalProcessor();
    ~CSignalProcessor();
    
    // Method to process incoming trading signals
    void ProcessTradingSignal(TradingSignal &signal);
};

//+------------------------------------------------------------------+
//| Default constructor                                              |
//+------------------------------------------------------------------+
CSignalProcessor::CSignalProcessor() {
    // Initialize trade object
    m_trade.SetExpertMagicNumber(123456); // Example magic number
    m_trade.SetDeviationInPoints(10);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CSignalProcessor::~CSignalProcessor() {
    // Nothing to do here
}

//+------------------------------------------------------------------+
//| Process Trading Signal                                           |
//+------------------------------------------------------------------+
void CSignalProcessor::ProcessTradingSignal(TradingSignal &signal) {
    string signalTypeStr = "";
    switch(signal.signal_type) {
        case SIGNAL_TYPE_BUY:
            signalTypeStr = "BUY";
            break;
        case SIGNAL_TYPE_SELL:
            signalTypeStr = "SELL";
            break;
        case SIGNAL_TYPE_HOLD:
            signalTypeStr = "HOLD";
            break;
    }
    
    string strategyName = CharArrayToString(signal.strategy_name);
    string parameters = CharArrayToString(signal.parameters);
    
    Print("=== TRADING SIGNAL PROCESSED ===");
    Print("Signal Type: ", signalTypeStr);
    Print("Strategy: ", strategyName);
    Print("Confidence: ", DoubleToString(signal.confidence_level, 2));
    Print("Stop Loss: ", DoubleToString(signal.stop_loss, _Digits));
    Print("Take Profit: ", DoubleToString(signal.take_profit, _Digits));
    Print("Parameters: ", parameters);
    Print("================================");
}

//+------------------------------------------------------------------+
//| Create Trading Signal                                            |
//+------------------------------------------------------------------+
TradingSignal CreateTradingSignal(ENUM_SIGNAL_TYPE type, double confidence, 
                                 double sl, double tp, string params, string strategy) {
    TradingSignal signal;
    signal.signal_type = type;
    signal.confidence_level = confidence;
    signal.stop_loss = sl;
    signal.take_profit = tp;
    
    // Convert strings to char arrays for binary compatibility
    StringToCharArray(params, signal.parameters, 0, 255);
    StringToCharArray(strategy, signal.strategy_name, 0, 63);
    
    return signal;
}

//+------------------------------------------------------------------+
//| Get Signal Type String - REMOVED DUPLICATE DEFINITION           |
//+------------------------------------------------------------------+