//+------------------------------------------------------------------+
//|                                                  Dashboard.mqh |
//|                        Dashboard logic for <PERSON><PERSON><PERSON>            |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"

#include "SharedDefs.mqh"

//--- Dashboard UI/UX Enhancements
#define DASHBOARD_PREFIX "MisapeBot_Dashboard_v2_"
#define CARD_WIDTH 220
#define CARD_HEIGHT 100
#define SPACING_X 15
#define SPACING_Y 15
#define START_X 25
#define START_Y 60
#define CORNER_RADIUS 5

//--- Professional Color Palette (Dark Theme)
#define COLOR_BACKGROUND clrBlack
#define COLOR_CARD_BG 0x2B2B2B // Dark Gray
#define COLOR_CARD_BORDER 0x4A4A4A // Medium Gray
#define COLOR_TEXT_HEADER clrWhite
#define COLOR_TEXT_NORMAL clrSilver
#define COLOR_TEXT_ACCENT clrDodgerBlue
#define COLOR_BUY clrSeaGreen
#define COLOR_SELL clrCrimson
#define COLOR_HOLD clrGoldenrod
#define COLOR_PROFIT clrLimeGreen
#define COLOR_LOSS clrFireBrick

//+------------------------------------------------------------------+
//| Helper function to create a text label                           |
//+------------------------------------------------------------------+
void CreateLabel(string name, int x, int y, string text, string font = "Arial", int size = 10, color clr = clrWhite, bool is_bold = false) {
    ObjectCreate(0, name, OBJ_LABEL, 0, x, y);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    string font_style = font;
    if(is_bold) font_style += ";bold";
    ObjectSetString(0, name, OBJPROP_FONT, font_style);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, size);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_BACK, false);
}

//+------------------------------------------------------------------+
//| Helper function to update a text label                           |
//+------------------------------------------------------------------+
void UpdateLabel(string name, string text, color clr = clrNONE) {
    if (ObjectFind(0, name) < 0) return;
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    if (clr != clrNONE) {
        ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    }
}

//+------------------------------------------------------------------+
//| Helper function to create a styled panel                         |
//+------------------------------------------------------------------+
void CreatePanel(string name, int x, int y, int w, int h, string title) {
    //--- Panel Background
    ObjectCreate(0, name + "_Bg", OBJ_RECTANGLE_LABEL, 0, x, y);
    ObjectSetInteger(0, name + "_Bg", OBJPROP_XSIZE, w);
    ObjectSetInteger(0, name + "_Bg", OBJPROP_YSIZE, h);
    ObjectSetInteger(0, name + "_Bg", OBJPROP_COLOR, COLOR_CARD_BG);
    ObjectSetInteger(0, name + "_Bg", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, name + "_Bg", OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name + "_Bg", OBJPROP_SELECTABLE, false);

    //--- Panel Border
    ObjectCreate(0, name + "_Border", OBJ_RECTANGLE_LABEL, 0, x, y);
    ObjectSetInteger(0, name + "_Border", OBJPROP_XSIZE, w);
    ObjectSetInteger(0, name + "_Border", OBJPROP_YSIZE, h);
    ObjectSetInteger(0, name + "_Border", OBJPROP_COLOR, COLOR_CARD_BORDER);
    ObjectSetInteger(0, name + "_Border", OBJPROP_FILL, false);
    ObjectSetInteger(0, name + "_Border", OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name + "_Border", OBJPROP_SELECTABLE, false);

    //--- Panel Title
    CreateLabel(name + "_Title", x + 10, y + 10, title, "Arial", 12, COLOR_TEXT_HEADER, true);
}

//+------------------------------------------------------------------+
//| Create Dashboard Objects                                         |
//+------------------------------------------------------------------+
void CreateDashboard() {
    ObjectsDeleteAll(0, DASHBOARD_PREFIX); // Clear existing dashboard objects
    int chart_id = 0;

    //--- Main Dashboard Background
    ObjectCreate(chart_id, DASHBOARD_PREFIX + "MainBg", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(chart_id, DASHBOARD_PREFIX + "MainBg", OBJPROP_XSIZE, 3000); // Very wide to cover all chart
    ObjectSetInteger(chart_id, DASHBOARD_PREFIX + "MainBg", OBJPROP_YSIZE, 2000); // Very tall to cover all chart
    ObjectSetInteger(chart_id, DASHBOARD_PREFIX + "MainBg", OBJPROP_COLOR, COLOR_BACKGROUND);
    ObjectSetInteger(chart_id, DASHBOARD_PREFIX + "MainBg", OBJPROP_BACK, true);
    ObjectSetInteger(chart_id, DASHBOARD_PREFIX + "MainBg", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(chart_id, DASHBOARD_PREFIX + "MainBg", OBJPROP_CORNER, CORNER_LEFT_UPPER);

    //--- Dashboard Title
    CreateLabel(DASHBOARD_PREFIX + "Title", START_X, START_Y - 35, "MISAPE BOT - SENTINEL DASHBOARD", "Arial Black", 16, COLOR_TEXT_ACCENT, false);

    //--- Main Status Panel
    int main_panel_x = START_X + (CARD_WIDTH + SPACING_X) * 2 + SPACING_X;
    int main_panel_y = START_Y;
    int main_panel_w = (int)(CARD_WIDTH * 1.5);
    int main_panel_h = (int)(CARD_HEIGHT * 2 + SPACING_Y);

    CreatePanel(DASHBOARD_PREFIX + "MainStatusPanel", main_panel_x, main_panel_y, main_panel_w, main_panel_h, "Master Status");
    CreateLabel(DASHBOARD_PREFIX + "StatusLabel", main_panel_x + 15, main_panel_y + 40, "Bot Status:", "Arial", 10, COLOR_TEXT_NORMAL);
    CreateLabel(DASHBOARD_PREFIX + "StatusValue", main_panel_x + 100, main_panel_y + 40, "OPERATIONAL", "Arial", 10, COLOR_BUY);
    CreateLabel(DASHBOARD_PREFIX + "TradesLabel", main_panel_x + 15, main_panel_y + 65, "Active Trades:", "Arial", 10, COLOR_TEXT_NORMAL);
    CreateLabel(DASHBOARD_PREFIX + "TradesValue", main_panel_x + 100, main_panel_y + 65, "0", "Arial", 10, COLOR_TEXT_HEADER);
    CreateLabel(DASHBOARD_PREFIX + "ProfitLabel", main_panel_x + 15, main_panel_y + 90, "Total P/L:", "Arial", 10, COLOR_TEXT_NORMAL);
    CreateLabel(DASHBOARD_PREFIX + "ProfitValue", main_panel_x + 100, main_panel_y + 90, "$0.00", "Arial", 10, COLOR_TEXT_HEADER);

    //--- Create Satellite Bot Cards (2-column layout)
    for (int i = 0; i < ArraySize(g_signal_statuses); i++) {
        int col = i % 2;
        int row = i / 2;
        int x_pos = START_X + col * (CARD_WIDTH + SPACING_X);
        int y_pos = START_Y + row * (CARD_HEIGHT + SPACING_Y);
        string card_name = DASHBOARD_PREFIX + g_signal_statuses[i].strategy_name;

        CreatePanel(card_name, x_pos, y_pos, CARD_WIDTH, CARD_HEIGHT, g_signal_statuses[i].strategy_name);

        // Signal Info
        CreateLabel(card_name + "_SignalLabel", x_pos + 15, y_pos + 40, "Signal:", "Arial", 10, COLOR_TEXT_NORMAL);
        CreateLabel(card_name + "_SignalValue", x_pos + 90, y_pos + 40, "N/A", "Arial", 10, COLOR_HOLD, true);

        // Confidence Info
        CreateLabel(card_name + "_ConfLabel", x_pos + 15, y_pos + 60, "Confidence:", "Arial", 10, COLOR_TEXT_NORMAL);
        CreateLabel(card_name + "_ConfValue", x_pos + 90, y_pos + 60, "0.00", "Arial", 10, COLOR_TEXT_HEADER);

        // Last Update Info
        CreateLabel(card_name + "_UpdateLabel", x_pos + 15, y_pos + 80, "Last Update:", "Arial", 8, COLOR_TEXT_NORMAL);
        CreateLabel(card_name + "_UpdateValue", x_pos + 90, y_pos + 80, "-", "Arial", 8, COLOR_TEXT_NORMAL);
    }
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| Update Dashboard Objects                                         |
//+------------------------------------------------------------------+
void UpdateDashboard() {
    //--- Update Satellite Bot Cards
    for (int i = 0; i < ArraySize(g_signal_statuses); i++) {
        string card_name = DASHBOARD_PREFIX + g_signal_statuses[i].strategy_name;
        string signal_val, conf_val, update_val;
        color signal_color, border_color;

        if (g_signal_statuses[i].is_valid) {
            TradingSignal signal = g_signal_statuses[i].last_signal;
            signal_val = GetSignalTypeString(signal.signal_type);
            conf_val = DoubleToString(signal.confidence_level, 2);
            update_val = TimeToString(g_signal_statuses[i].last_updated, TIME_SECONDS);

            switch(signal.signal_type) {
                case SIGNAL_TYPE_BUY:  signal_color = COLOR_BUY;  border_color = COLOR_BUY;  break;
                case SIGNAL_TYPE_SELL: signal_color = COLOR_SELL; border_color = COLOR_SELL; break;
                default:               signal_color = COLOR_HOLD; border_color = COLOR_CARD_BORDER; break;
            }
        } else {
            signal_val = "N/A";
            conf_val = "0.00";
            update_val = "-";
            signal_color = COLOR_TEXT_NORMAL;
            border_color = COLOR_CARD_BORDER;
        }

        UpdateLabel(card_name + "_SignalValue", signal_val, signal_color);
        UpdateLabel(card_name + "_ConfValue", conf_val);
        UpdateLabel(card_name + "_UpdateValue", update_val);
        ObjectSetInteger(0, card_name + "_Border", OBJPROP_COLOR, border_color);
    }

    //--- Update Main Status Panel
    int active_trades = PositionsTotal();
    double total_pl = 0;
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
            total_pl += PositionGetDouble(POSITION_PROFIT);
        }
    }

    UpdateLabel(DASHBOARD_PREFIX + "TradesValue", (string)active_trades);
    UpdateLabel(DASHBOARD_PREFIX + "ProfitValue", "$" + DoubleToString(total_pl, 2), total_pl >= 0 ? COLOR_PROFIT : COLOR_LOSS);
    UpdateLabel(DASHBOARD_PREFIX + "StatusValue", EnableTrading ? "TRADING ACTIVE" : "TRADING DISABLED", EnableTrading ? COLOR_BUY : COLOR_SELL);

    ChartRedraw();
}