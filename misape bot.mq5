//+------------------------------------------------------------------+
//|                                                   misape bot.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Central signal aggregator and trading bot for My Bots EAs"
#property strict

#include <Trade/Trade.mqh>
#include "SignalProcessor.mqh"
#include "SignalFileManager.mqh"
#include "SharedDefs.mqh"
#include "Dashboard.mqh"

//--- Input parameters
input group "=== Misape <PERSON> Settings ==="
input long MagicNumber = 789012;       // Magic number for Misa<PERSON> trades
input double DefaultLotSize = 0.1;     // Default lot size if not specified by signal
input int SignalExpiryMinutes = 5;     // Minutes before a signal is considered expired
input bool EnableTrading = false;      // Master switch to enable/disable trading
input bool EnableDashboard = true;     // Enable/disable the visual dashboard

input group "=== Aggregation Settings ===";
input int MinSignalConsensus = 2;      // Minimum number of signals required for consensus
input double MinConfidenceThreshold = 0.65; // Minimum average confidence level for consensus trade

input group "=== Risk Management Settings ===";
input double RiskPercent = 1.0; // Percentage of account equity to risk per trade

//--- Satellite Bot Configuration
#define MAX_STRATEGIES 5

input group "=== Satellite Bot 1 Settings ===";
input bool   Strategy1_Enabled = true;
input string Strategy1_Name = "Fair forex look";
input string Strategy1_File = SIGNAL_FILE_FAIR_FOREX;

input group "=== Satellite Bot 2 Settings ===";
input bool   Strategy2_Enabled = true;
input string Strategy2_Name = "Advanced Order Block";
input string Strategy2_File = SIGNAL_FILE_ORDER_BLOCK;

input group "=== Satellite Bot 3 Settings ===";
input bool   Strategy3_Enabled = true;
input string Strategy3_Name = "Market Structure";
input string Strategy3_File = SIGNAL_FILE_MARKET_STRUCTURE;

input group "=== Satellite Bot 4 Settings ===";
input bool   Strategy4_Enabled = true;
input string Strategy4_Name = "Range Breakout";
input string Strategy4_File = SIGNAL_FILE_RANGE_BREAKOUT;

input group "=== Satellite Bot 5 Settings ===";
input bool   Strategy5_Enabled = true;
input string Strategy5_Name = "Support and Resistance";
input string Strategy5_File = SIGNAL_FILE_SUPPORT_RESISTANCE;

//--- Global variables

StrategyConfig g_strategy_configs[MAX_STRATEGIES];
SignalStatus   g_signal_statuses[]; // Dynamically sized array

CTrade m_trade; // Trade object for executing trades

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    //--- Set chart background color to black
    ChartSetInteger(0, CHART_COLOR_BACKGROUND, clrBlack);
    //--- Initialize Trade object
    m_trade.SetExpertMagicNumber(MagicNumber);

    //--- Load strategy configurations from inputs
    LoadStrategyConfigs();

    //--- Initialize signal statuses for enabled strategies
    int enabled_count = 0;
    for (int i = 0; i < MAX_STRATEGIES; i++) {
        if (g_strategy_configs[i].is_enabled) {
            enabled_count++;
        }
    }
    ArrayResize(g_signal_statuses, enabled_count);

    int current_status_index = 0;
    for (int i = 0; i < MAX_STRATEGIES; i++) {
        if (g_strategy_configs[i].is_enabled) {
            g_signal_statuses[current_status_index].strategy_name = g_strategy_configs[i].name;
            g_signal_statuses[current_status_index].signal_file = g_strategy_configs[i].signal_file;
            g_signal_statuses[current_status_index].is_valid = false;
            g_signal_statuses[current_status_index].last_updated = 0;
            g_signal_statuses[current_status_index].status_text = "Initializing...";
            g_signal_statuses[current_status_index].status_color = clrGray;
            // Initialize TradingSignal struct members
            g_signal_statuses[current_status_index].last_signal.signal_type = SIGNAL_TYPE_HOLD;
            g_signal_statuses[current_status_index].last_signal.confidence_level = 0.0;
            g_signal_statuses[current_status_index].last_signal.stop_loss = 0.0;
            g_signal_statuses[current_status_index].last_signal.take_profit = 0.0;
            StringToCharArray("", g_signal_statuses[current_status_index].last_signal.parameters, 0, 255);
            StringToCharArray(g_strategy_configs[i].name, g_signal_statuses[current_status_index].last_signal.strategy_name, 0, 63);
            current_status_index++;
        }
    }

    //--- Create Dashboard objects if enabled
    if (EnableDashboard) {
        CreateDashboard();
    }

    Print("Misape Bot initialized successfully.");
    return (INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    //--- Clean up dashboard objects
    ObjectsDeleteAll(0, DASHBOARD_PREFIX);

    Print("Misape Bot deinitialized.");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    //--- Process signals from files
    ProcessSignalFiles();

    //--- Update Dashboard
    if (EnableDashboard) {
        UpdateDashboard();
    }

    //--- Execute trading logic (Implement your aggregation/decision logic here)
    if (EnableTrading) {
        ExecuteTradingLogic();
    }

    //--- Clean up old signal files periodically
    static datetime last_cleanup_time = 0;
    if (TimeCurrent() - last_cleanup_time > 600) { // Clean up every 10 minutes
        CleanupOldSignalFiles();
        last_cleanup_time = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| Process signal files                                             |
//+------------------------------------------------------------------+
void ProcessSignalFiles() {
    Print("[DEBUG] ProcessSignalFiles called at ", TimeToString(TimeCurrent(), TIME_SECONDS));
    for (int i = 0; i < ArraySize(g_signal_statuses); i++) {
        string filename = g_signal_statuses[i].signal_file;
        Print("[DEBUG] Checking file: ", filename, " for strategy: ", g_signal_statuses[i].strategy_name);
        TradingSignal signal;
        SignalFileHeader header;

        if (ReadSignalFromFile(filename, signal, header)) {
            Print("[DEBUG] ReadSignalFromFile SUCCESS for ", filename, ". Header timestamp: ", TimeToString(header.timestamp, TIME_SECONDS));
            // Check if this is a new or updated signal from this source
            if (header.timestamp > g_signal_statuses[i].last_updated) {
                g_signal_statuses[i].last_signal = signal;
                g_signal_statuses[i].last_updated = header.timestamp;
                g_signal_statuses[i].is_valid = (TimeCurrent() - header.timestamp) < (SignalExpiryMinutes * 60);
                Print("[DEBUG] Signal is_valid: ", g_signal_statuses[i].is_valid, ", status_text: ", g_signal_statuses[i].status_text);
                if(g_signal_statuses[i].is_valid){
                    g_signal_statuses[i].status_text = "Signal Received";
                    g_signal_statuses[i].status_color = clrGreen;
                    PrintFormat("Received new signal from %s: %s", g_signal_statuses[i].strategy_name, GetSignalTypeString(signal.signal_type));
                } else {
                    g_signal_statuses[i].status_text = "Signal Expired";
                    g_signal_statuses[i].status_color = clrOrange;
                    PrintFormat("Signal from %s has expired.", g_signal_statuses[i].strategy_name);
                }
            }
        } else {
            Print("[DEBUG] ReadSignalFromFile FAILED for ", filename);
            if (g_signal_statuses[i].is_valid || g_signal_statuses[i].status_text != "File Error") {
                 PrintFormat("Error reading signal file for %s. File: %s", g_signal_statuses[i].strategy_name, filename);
            }
            g_signal_statuses[i].is_valid = false;
            g_signal_statuses[i].status_text = "File Error";
            g_signal_statuses[i].status_color = clrRed;
        }
    }
}

//+------------------------------------------------------------------+
//| Execute trading logic                                            |
//+------------------------------------------------------------------+
void ExecuteTradingLogic() {
    if (PositionsTotal() > 0) return; // Avoid opening multiple positions

    int buy_signals = 0;
    double buy_confidence_sum = 0;
    int sell_signals = 0;
    double sell_confidence_sum = 0;

    //--- Step 1: Tally all valid BUY and SELL signals
    for (int i = 0; i < ArraySize(g_signal_statuses); i++) {
        if (g_signal_statuses[i].is_valid) {
            TradingSignal signal = g_signal_statuses[i].last_signal;
            if (signal.signal_type == SIGNAL_TYPE_BUY) {
                buy_signals++;
                buy_confidence_sum += signal.confidence_level;
            } else if (signal.signal_type == SIGNAL_TYPE_SELL) {
                sell_signals++;
                sell_confidence_sum += signal.confidence_level;
            }
        }
    }

    //--- Step 2: Check for BUY consensus
    if (buy_signals >= MinSignalConsensus) {
        double avg_confidence = buy_confidence_sum / buy_signals;
        if (avg_confidence >= MinConfidenceThreshold) {
            TradingSignal consensus_signal;
            consensus_signal.signal_type = SIGNAL_TYPE_BUY;
            consensus_signal.confidence_level = avg_confidence;
            // For SL/TP, you could average them, or use the one from the highest confidence signal.
            // Here, we'll just use the default for simplicity.
            consensus_signal.stop_loss = 0; 
            consensus_signal.take_profit = 0;
            ExecuteTrade(consensus_signal, "Consensus BUY Signal");
            return; // Exit after executing trade
        }
    }

    //--- Step 3: Check for SELL consensus
    if (sell_signals >= MinSignalConsensus) {
        double avg_confidence = sell_confidence_sum / sell_signals;
        if (avg_confidence >= MinConfidenceThreshold) {
            TradingSignal consensus_signal;
            consensus_signal.signal_type = SIGNAL_TYPE_SELL;
            consensus_signal.confidence_level = avg_confidence;
            consensus_signal.stop_loss = 0;
            consensus_signal.take_profit = 0;
            ExecuteTrade(consensus_signal, "Consensus SELL Signal");
            return; // Exit after executing trade
        }
    }
}

//+------------------------------------------------------------------+
//| Helper function to execute a trade based on a signal             |
//+------------------------------------------------------------------+
void ExecuteTrade(const TradingSignal &signal, string strategy_name) {
    Print("Executing ", GetSignalTypeString(signal.signal_type), " based on ", strategy_name, " signal.");

    double sl_price = signal.stop_loss;
    double tp_price = signal.take_profit;
    double lot_size = DefaultLotSize;

    //--- Dynamic Lot Size Calculation
    if (sl_price > 0 && RiskPercent > 0) {
        double current_price = (signal.signal_type == SIGNAL_TYPE_BUY) ? SymbolInfoDouble(_Symbol, SYMBOL_ASK) : SymbolInfoDouble(_Symbol, SYMBOL_BID);
        double sl_pips = MathAbs(current_price - sl_price) / _Point;
        if (sl_pips > 0) {
            double risk_amount = AccountInfoDouble(ACCOUNT_EQUITY) * (RiskPercent / 100.0);
            double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
            double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
            double pips_to_money = sl_pips * (tick_value / tick_size);
            if (pips_to_money > 0) {
                lot_size = NormalizeDouble(risk_amount / pips_to_money, 2);
            }
        }
    }
    
    //--- Lot size validation
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    lot_size = MathMax(min_lot, lot_size);
    lot_size = MathMin(max_lot, lot_size);
    lot_size = NormalizeDouble(MathFloor(lot_size / lot_step) * lot_step, 2);

    if (lot_size < min_lot) {
        Print("Calculated lot size is too small. Using minimum lot size.");
        lot_size = min_lot;
    }

    bool trade_result = false;
    if (signal.signal_type == SIGNAL_TYPE_BUY) {
        trade_result = m_trade.Buy(lot_size, _Symbol, SymbolInfoDouble(_Symbol, SYMBOL_ASK), sl_price, tp_price, strategy_name);
    } else if (signal.signal_type == SIGNAL_TYPE_SELL) {
        trade_result = m_trade.Sell(lot_size, _Symbol, SymbolInfoDouble(_Symbol, SYMBOL_BID), sl_price, tp_price, strategy_name);
    }

    if(trade_result){
        PrintFormat("Trade executed successfully: %s at %f, Lot: %f, SL: %f, TP: %f", 
                    strategy_name, m_trade.ResultPrice(), m_trade.ResultVolume(), sl_price, tp_price);
    } else {
        PrintFormat("Trade execution failed for %s. Error: %d - %s", 
                    strategy_name, m_trade.ResultRetcode(), m_trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Load strategy configurations from input parameters               |
//+------------------------------------------------------------------+
void LoadStrategyConfigs() {
    g_strategy_configs[0].is_enabled = Strategy1_Enabled;
    g_strategy_configs[0].name = Strategy1_Name;
    g_strategy_configs[0].signal_file = Strategy1_File;

    g_strategy_configs[1].is_enabled = Strategy2_Enabled;
    g_strategy_configs[1].name = Strategy2_Name;
    g_strategy_configs[1].signal_file = Strategy2_File;

    g_strategy_configs[2].is_enabled = Strategy3_Enabled;
    g_strategy_configs[2].name = Strategy3_Name;
    g_strategy_configs[2].signal_file = Strategy3_File;

    g_strategy_configs[3].is_enabled = Strategy4_Enabled;
    g_strategy_configs[3].name = Strategy4_Name;
    g_strategy_configs[3].signal_file = Strategy4_File;

    g_strategy_configs[4].is_enabled = Strategy5_Enabled;
    g_strategy_configs[4].name = Strategy5_Name;
    g_strategy_configs[4].signal_file = Strategy5_File;
}