//+------------------------------------------------------------------+
//|                                        Advanced_Order_Block_EA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"
#property indicator_chart_window
#property indicator_plots 0

// Add signal arrows
#property indicator_buffers 2
#property indicator_color1 clrLime
#property indicator_color2 clrRed
#property indicator_type1 DRAW_ARROW
#property indicator_type2 DRAW_ARROW
#property indicator_width1 3
#property indicator_width2 3

//--- Input Parameters
input group "=== Swing Detection Settings ==="
input int SwingLength = 5;                    // Minimum candles on each side for swing
input int MinCandleSize = 100;               // Minimum candle size in points
input bool UseVolumeFilter = true;           // Enable volume filtering
input int MinVolumeMultiplier = 2;           // Minimum volume multiplier vs average

input group "=== Multi-Timeframe Settings ==="
input bool ShowH1Blocks = true;              // Show H1 Order Blocks
input bool ShowH4Blocks = true;              // Show H4 Order Blocks
input bool ShowD1Blocks = true;              // Show Daily Order Blocks
input bool ShowCurrentTFBlocks = true;       // Show Current Timeframe Blocks

input group "=== Block Validation Settings ==="
input int MinDistanceFromPrice = 500;        // Minimum distance from current price (points)
input int MaxBlockAge = 168;                 // Maximum block age in hours
input double MinReactionSize = 0.5;          // Minimum reaction size (% of ATR)

input group "=== Visual Settings ==="
input color BullishBlockColor = clrDodgerBlue;     // Bullish block color
input color BearishBlockColor = clrCrimson;        // Bearish block color
input color H1BlockColor = clrLightBlue;           // H1 timeframe color
input color H4BlockColor = clrOrange;              // H4 timeframe color
input color D1BlockColor = clrGold;                // Daily timeframe color
input int BlockTransparency = 70;                  // Block transparency (0-100)
input bool ShowBlockLabels = true;                 // Show block information labels
input bool EnableAlerts = true;                    // Enable price approach alerts

input group "=== Signal Settings ==="
input bool ShowBuySignals = true;                  // Show BUY signal arrows
input bool ShowSellSignals = true;                 // Show SELL signal arrows
input bool SendPopupAlerts = true;                 // Send popup alerts for signals
input bool PlaySoundAlerts = true;                 // Play sound for signals

input group "=== Trading Settings ==="
input bool AutoTrade = false;                      // Enable automatic trading
input double LotSize = 0.01;                       // Trading lot size
input double RiskPercent = 2.0;                    // Risk percentage per trade
input int MaxOpenTrades = 3;                       // Maximum open trades
input double MinEquity = 100.0;                    // Minimum account equity to trade

//--- Signal buffers
double BuySignalBuffer[];
double SellSignalBuffer[];

//--- Global Variables
struct OrderBlock {
    datetime creation_time;
    double high_price;
    double low_price;
    ENUM_TIMEFRAMES timeframe;
    bool is_bullish;
    int touches;
    double strength;
    bool is_fresh;
    bool is_broken;
    string obj_name;
    datetime last_touch;
    double partial_fill_ratio;
    bool signal_sent;
};

OrderBlock g_blocks[];
int g_block_count = 0;
datetime g_last_bar_time = 0;
double g_atr_value = 0;
int g_atr_handle = INVALID_HANDLE;

//--- Multi-timeframe handles
int g_h1_bars = 0, g_h4_bars = 0, g_d1_bars = 0;
datetime g_h1_time[], g_h4_time[], g_d1_time[];
double g_h1_open[], g_h1_high[], g_h1_low[], g_h1_close[];
double g_h4_open[], g_h4_high[], g_h4_low[], g_h4_close[];
double g_d1_open[], g_d1_high[], g_d1_low[], g_d1_close[];
long g_h1_volume[], g_h4_volume[], g_d1_volume[];

#include <Trade/Trade.mqh>
CTrade trade;

#include "SignalProcessor.mqh"
#include "SignalFileManager.mqh"

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    // Set up signal buffers
    SetIndexBuffer(0, BuySignalBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, SellSignalBuffer, INDICATOR_DATA);
    
    PlotIndexSetInteger(0, PLOT_ARROW, 233);  // Up arrow for buy
    PlotIndexSetInteger(1, PLOT_ARROW, 234);  // Down arrow for sell
    
    ArraySetAsSeries(BuySignalBuffer, true);
    ArraySetAsSeries(SellSignalBuffer, true);
    
    // Initialize signal buffers
    ArrayInitialize(BuySignalBuffer, EMPTY_VALUE);
    ArrayInitialize(SellSignalBuffer, EMPTY_VALUE);
    
    // Initialize ATR for strength calculations
    g_atr_handle = iATR(_Symbol, _Period, 14);
    if(g_atr_handle == INVALID_HANDLE) {
        Print("Failed to create ATR indicator handle");
        return INIT_FAILED;
    }
    
    // Initialize arrays
    ArrayResize(g_blocks, 0);
    g_block_count = 0;
    
    // Set up multi-timeframe data arrays
    ArraySetAsSeries(g_h1_time, true);
    ArraySetAsSeries(g_h1_open, true);
    ArraySetAsSeries(g_h1_high, true);
    ArraySetAsSeries(g_h1_low, true);
    ArraySetAsSeries(g_h1_close, true);
    ArraySetAsSeries(g_h1_volume, true);
    
    ArraySetAsSeries(g_h4_time, true);
    ArraySetAsSeries(g_h4_open, true);
    ArraySetAsSeries(g_h4_high, true);
    ArraySetAsSeries(g_h4_low, true);
    ArraySetAsSeries(g_h4_close, true);
    ArraySetAsSeries(g_h4_volume, true);
    
    ArraySetAsSeries(g_d1_time, true);
    ArraySetAsSeries(g_d1_open, true);
    ArraySetAsSeries(g_d1_high, true);
    ArraySetAsSeries(g_d1_low, true);
    ArraySetAsSeries(g_d1_close, true);
    ArraySetAsSeries(g_d1_volume, true);
    
    Print("Advanced Order Block EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Clean up all order block objects
    for(int i = 0; i < g_block_count; i++) {
        if(ObjectFind(0, g_blocks[i].obj_name) >= 0) {
            ObjectDelete(0, g_blocks[i].obj_name);
            ObjectDelete(0, g_blocks[i].obj_name + "_label");
        }
    }
    
    // Release ATR handle
    if(g_atr_handle != INVALID_HANDLE) {
        IndicatorRelease(g_atr_handle);
    }
    
    ChartRedraw();
    Print("Advanced Order Block EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // Check for new bar
    datetime current_time = iTime(_Symbol, _Period, 0);
    if(current_time == g_last_bar_time) return;
    g_last_bar_time = current_time;
    
    // Update ATR value
    double atr_buffer[1];
    if(CopyBuffer(g_atr_handle, 0, 1, 1, atr_buffer) > 0) {
        g_atr_value = atr_buffer[0];
    }
    
    // Update multi-timeframe data
    UpdateMultiTimeframeData();
    
    // Detect new order blocks on current timeframe
    if(ShowCurrentTFBlocks) {
        DetectOrderBlocks(_Period);
    }
    
    // Detect order blocks on higher timeframes
    if(ShowH1Blocks && _Period < PERIOD_H1) {
        DetectOrderBlocks(PERIOD_H1);
    }
    if(ShowH4Blocks && _Period < PERIOD_H4) {
        DetectOrderBlocks(PERIOD_H4);
    }
    if(ShowD1Blocks && _Period < PERIOD_D1) {
        DetectOrderBlocks(PERIOD_D1);
    }
    
    // Update existing blocks
    UpdateOrderBlocks();
    
    // Check for trading opportunities
    if(AutoTrade) {
        CheckTradingSignals();
    }
    
    // Clean up old/invalid blocks
    CleanupOrderBlocks();
}

//+------------------------------------------------------------------+
//| Update multi-timeframe data                                      |
//+------------------------------------------------------------------+
void UpdateMultiTimeframeData() {
    // H1 Data
    CopyTime(_Symbol, PERIOD_H1, 0, SwingLength * 3, g_h1_time);
    CopyOpen(_Symbol, PERIOD_H1, 0, SwingLength * 3, g_h1_open);
    CopyHigh(_Symbol, PERIOD_H1, 0, SwingLength * 3, g_h1_high);
    CopyLow(_Symbol, PERIOD_H1, 0, SwingLength * 3, g_h1_low);
    CopyClose(_Symbol, PERIOD_H1, 0, SwingLength * 3, g_h1_close);
    CopyTickVolume(_Symbol, PERIOD_H1, 0, SwingLength * 3, g_h1_volume);
    
    // H4 Data
    CopyTime(_Symbol, PERIOD_H4, 0, SwingLength * 3, g_h4_time);
    CopyOpen(_Symbol, PERIOD_H4, 0, SwingLength * 3, g_h4_open);
    CopyHigh(_Symbol, PERIOD_H4, 0, SwingLength * 3, g_h4_high);
    CopyLow(_Symbol, PERIOD_H4, 0, SwingLength * 3, g_h4_low);
    CopyClose(_Symbol, PERIOD_H4, 0, SwingLength * 3, g_h4_close);
    CopyTickVolume(_Symbol, PERIOD_H4, 0, SwingLength * 3, g_h4_volume);
    
    // D1 Data
    CopyTime(_Symbol, PERIOD_D1, 0, SwingLength * 3, g_d1_time);
    CopyOpen(_Symbol, PERIOD_D1, 0, SwingLength * 3, g_d1_open);
    CopyHigh(_Symbol, PERIOD_D1, 0, SwingLength * 3, g_d1_high);
    CopyLow(_Symbol, PERIOD_D1, 0, SwingLength * 3, g_d1_low);
    CopyClose(_Symbol, PERIOD_D1, 0, SwingLength * 3, g_d1_close);
    CopyTickVolume(_Symbol, PERIOD_D1, 0, SwingLength * 3, g_d1_volume);
}

//+------------------------------------------------------------------+
//| Detect order blocks on specified timeframe                       |
//+------------------------------------------------------------------+
void DetectOrderBlocks(ENUM_TIMEFRAMES tf) {
    double high[], low[], open[], close[];
    datetime time[];
    long volume[];
    
    // Get data for the specified timeframe
    GetTimeframeData(tf, time, open, high, low, close, volume);
    
    int bars_count = ArraySize(high);
    if(bars_count < SwingLength * 3) return;
    
    // Scan for swing points
    for(int i = SwingLength; i < bars_count - SwingLength - 1; i++) {
        // Check for swing high
        if(IsSwingHigh(high, i, SwingLength)) {
            // Look for bullish order block (last bearish candle before bullish break)
            int ob_index = FindOrderBlockCandle(open, close, high, low, volume, i, true, tf);
            if(ob_index > 0) {
                CreateOrderBlock(time[ob_index], high[ob_index], low[ob_index], 
                               tf, true, open[ob_index], close[ob_index]);
            }
        }
        
        // Check for swing low
        if(IsSwingLow(low, i, SwingLength)) {
            // Look for bearish order block (last bullish candle before bearish break)
            int ob_index = FindOrderBlockCandle(open, close, high, low, volume, i, false, tf);
            if(ob_index > 0) {
                CreateOrderBlock(time[ob_index], high[ob_index], low[ob_index], 
                               tf, false, open[ob_index], close[ob_index]);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Get timeframe data                                               |
//+------------------------------------------------------------------+
void GetTimeframeData(ENUM_TIMEFRAMES tf, datetime &time[], double &open[], 
                     double &high[], double &low[], double &close[], long &volume[]) {
    switch(tf) {
        case PERIOD_H1:
            ArrayCopy(time, g_h1_time);
            ArrayCopy(open, g_h1_open);
            ArrayCopy(high, g_h1_high);
            ArrayCopy(low, g_h1_low);
            ArrayCopy(close, g_h1_close);
            ArrayCopy(volume, g_h1_volume);
            break;
        case PERIOD_H4:
            ArrayCopy(time, g_h4_time);
            ArrayCopy(open, g_h4_open);
            ArrayCopy(high, g_h4_high);
            ArrayCopy(low, g_h4_low);
            ArrayCopy(close, g_h4_close);
            ArrayCopy(volume, g_h4_volume);
            break;
        case PERIOD_D1:
            ArrayCopy(time, g_d1_time);
            ArrayCopy(open, g_d1_open);
            ArrayCopy(high, g_d1_high);
            ArrayCopy(low, g_d1_low);
            ArrayCopy(close, g_d1_close);
            ArrayCopy(volume, g_d1_volume);
            break;
        default:
            CopyTime(_Symbol, tf, 0, SwingLength * 3, time);
            CopyOpen(_Symbol, tf, 0, SwingLength * 3, open);
            CopyHigh(_Symbol, tf, 0, SwingLength * 3, high);
            CopyLow(_Symbol, tf, 0, SwingLength * 3, low);
            CopyClose(_Symbol, tf, 0, SwingLength * 3, close);
            CopyTickVolume(_Symbol, tf, 0, SwingLength * 3, volume);
            break;
    }
}

//+------------------------------------------------------------------+
//| Check if index is a swing high                                   |
//+------------------------------------------------------------------+
bool IsSwingHigh(const double &high[], int index, int length) {
    for(int i = 1; i <= length; i++) {
        if(high[index] <= high[index - i] || high[index] <= high[index + i]) {
            return false;
        }
    }
    return true;
}

//+------------------------------------------------------------------+
//| Check if index is a swing low                                    |
//+------------------------------------------------------------------+
bool IsSwingLow(const double &low[], int index, int length) {
    for(int i = 1; i <= length; i++) {
        if(low[index] >= low[index - i] || low[index] >= low[index + i]) {
            return false;
        }
    }
    return true;
}

//+------------------------------------------------------------------+
//| Find order block candle before swing break                       |
//+------------------------------------------------------------------+
int FindOrderBlockCandle(const double &open[], const double &close[], 
                        const double &high[], const double &low[], 
                        const long &volume[], int swing_index, 
                        bool is_bullish_break, ENUM_TIMEFRAMES tf) {
    
    // Calculate average volume for filtering
    double avg_volume = 0;
    if(UseVolumeFilter) {
        for(int i = swing_index; i < swing_index + 20 && i < ArraySize(volume); i++) {
            avg_volume += (double)volume[i];
        }
        avg_volume /= MathMin(20, ArraySize(volume) - swing_index);
    }
    
    // Look for the last opposite candle before the break
    for(int i = swing_index + 1; i < ArraySize(open) - 1; i++) {
        bool is_opposite_candle = is_bullish_break ? (close[i] < open[i]) : (close[i] > open[i]);
        
        if(is_opposite_candle) {
            // Check candle size filter
            double candle_size = MathAbs(high[i] - low[i]) / _Point;
            if(candle_size < MinCandleSize) continue;
            
            // Check volume filter
            if(UseVolumeFilter && volume[i] < avg_volume * MinVolumeMultiplier) continue;
            
            // Check minimum reaction size
            double reaction_size = 0;
            if(is_bullish_break) {
                for(int j = swing_index; j >= 0 && j >= swing_index - 10; j--) {
                    reaction_size = MathMax(reaction_size, high[j] - low[i]);
                }
            } else {
                for(int j = swing_index; j >= 0 && j >= swing_index - 10; j--) {
                    reaction_size = MathMax(reaction_size, high[i] - low[j]);
                }
            }
            
            if(reaction_size >= g_atr_value * MinReactionSize) {
                return i;
            }
        }
    }
    
    return -1;
}

//+------------------------------------------------------------------+
//| Create new order block                                           |
//+------------------------------------------------------------------+
void CreateOrderBlock(datetime time, double high, double low, 
                     ENUM_TIMEFRAMES tf, bool is_bullish, 
                     double open, double close) {
    
    // Check minimum distance from current price
    double current_price = (SymbolInfoDouble(_Symbol, SYMBOL_BID) + SymbolInfoDouble(_Symbol, SYMBOL_ASK)) / 2;
    double block_center = (high + low) / 2;
    double distance = MathAbs(current_price - block_center) / _Point;
    
    if(distance < MinDistanceFromPrice) return;
    
    // Check if block already exists at this location
    for(int i = 0; i < g_block_count; i++) {
        if(MathAbs(g_blocks[i].high_price - high) < _Point && 
           MathAbs(g_blocks[i].low_price - low) < _Point &&
           g_blocks[i].timeframe == tf) {
            return; // Block already exists
        }
    }
    
    // Create new block
    ArrayResize(g_blocks, g_block_count + 1);
    
    g_blocks[g_block_count].creation_time = time;
    g_blocks[g_block_count].high_price = high;
    g_blocks[g_block_count].low_price = low;
    g_blocks[g_block_count].timeframe = tf;
    g_blocks[g_block_count].is_bullish = is_bullish;
    g_blocks[g_block_count].touches = 0;
    g_blocks[g_block_count].strength = CalculateBlockStrength(high, low, open, close, tf);
    g_blocks[g_block_count].is_fresh = true;
    g_blocks[g_block_count].is_broken = false;
    g_blocks[g_block_count].obj_name = StringFormat("OB_%s_%d_%d", 
        EnumToString(tf), (int)time, g_block_count);
    g_blocks[g_block_count].last_touch = 0;
    g_blocks[g_block_count].partial_fill_ratio = 0.0;
    g_blocks[g_block_count].signal_sent = false;
    
    // Create visual rectangle
    DrawOrderBlock(g_block_count);
    
    g_block_count++;
    
    Print(StringFormat("New %s Order Block created: %s TF, Price: %.5f-%.5f", 
        is_bullish ? "Bullish" : "Bearish", EnumToString(tf), low, high));
}

//+------------------------------------------------------------------+
//| Calculate block strength                                         |
//+------------------------------------------------------------------+
double CalculateBlockStrength(double high, double low, double open, 
                            double close, ENUM_TIMEFRAMES tf) {
    double strength = 1.0;
    
    // Base strength on candle size relative to ATR
    double candle_size = high - low;
    if(g_atr_value > 0) {
        strength += (candle_size / g_atr_value) * 0.5;
    }
    
    // Add strength based on timeframe (higher TF = higher strength)
    switch(tf) {
        case PERIOD_D1: strength += 3.0; break;
        case PERIOD_H4: strength += 2.0; break;
        case PERIOD_H1: strength += 1.0; break;
        default: strength += 0.5; break;
    }
    
    // Add strength based on candle body vs wick ratio
    double body_size = MathAbs(close - open);
    double body_ratio = (candle_size > 0) ? body_size / candle_size : 0;
    strength += body_ratio * 0.5;
    
    return strength;
}

//+------------------------------------------------------------------+
//| Draw order block on chart                                        |
//+------------------------------------------------------------------+
void DrawOrderBlock(int block_index) {
    string obj_name = g_blocks[block_index].obj_name;
    
    // Determine colors based on timeframe and direction
    color block_color = g_blocks[block_index].is_bullish ? BullishBlockColor : BearishBlockColor;
    
    switch(g_blocks[block_index].timeframe) {
        case PERIOD_H1: block_color = H1BlockColor; break;
        case PERIOD_H4: block_color = H4BlockColor; break;
        case PERIOD_D1: block_color = D1BlockColor; break;
    }
    
    // Create rectangle
    datetime end_time = g_blocks[block_index].creation_time + PeriodSeconds(g_blocks[block_index].timeframe) * 50;
    
    ObjectCreate(0, obj_name, OBJ_RECTANGLE, 0, 
                g_blocks[block_index].creation_time, g_blocks[block_index].low_price,
                end_time, g_blocks[block_index].high_price);
    
    ObjectSetInteger(0, obj_name, OBJPROP_COLOR, block_color);
    ObjectSetInteger(0, obj_name, OBJPROP_FILL, true);
    ObjectSetInteger(0, obj_name, OBJPROP_BACK, true);
    ObjectSetInteger(0, obj_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, obj_name, OBJPROP_HIDDEN, true);
    
    // Set transparency based on age and strength
    int transparency = CalculateTransparency(block_index);
    ObjectSetInteger(0, obj_name, OBJPROP_BGCOLOR, ColorWithTransparency(block_color, transparency));
    
    // Create label if enabled
    if(ShowBlockLabels) {
        CreateBlockLabel(block_index);
    }
}

//+------------------------------------------------------------------+
//| Create block information label                                   |
//+------------------------------------------------------------------+
void CreateBlockLabel(int block_index) {
    string label_name = g_blocks[block_index].obj_name + "_label";
    double label_price = (g_blocks[block_index].high_price + g_blocks[block_index].low_price) / 2;
    
    string label_text = StringFormat("%s %s\nS:%.1f T:%d %s", 
        EnumToString(g_blocks[block_index].timeframe),
        g_blocks[block_index].is_bullish ? "Bull" : "Bear",
        g_blocks[block_index].strength,
        g_blocks[block_index].touches,
        g_blocks[block_index].is_fresh ? "Fresh" : "Used");
    
    ObjectCreate(0, label_name, OBJ_TEXT, 0, g_blocks[block_index].creation_time, label_price);
    ObjectSetString(0, label_name, OBJPROP_TEXT, label_text);
    ObjectSetInteger(0, label_name, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, label_name, OBJPROP_FONTSIZE, 8);
    ObjectSetInteger(0, label_name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, label_name, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| Update existing order blocks                                     |
//+------------------------------------------------------------------+
void UpdateOrderBlocks() {
    double current_high = iHigh(_Symbol, _Period, 0);
    double current_low = iLow(_Symbol, _Period, 0);
    double current_close = iClose(_Symbol, _Period, 0);
    
    for(int i = 0; i < g_block_count; i++) {
        if(g_blocks[i].is_broken) continue;
        
        // Check if price is touching the block
        bool is_touching = (current_high >= g_blocks[i].low_price && 
                           current_low <= g_blocks[i].high_price);
        
        // Check for fresh signal at block
        bool is_fresh_signal = CheckForFreshSignal(i, current_close);
        
        if(is_touching) {
            g_blocks[i].touches++;
            g_blocks[i].last_touch = TimeCurrent();
            
            // Generate trading signal if conditions are met
            if(is_fresh_signal && !g_blocks[i].signal_sent) {
                GenerateTradingSignal(i, current_close);
                g_blocks[i].signal_sent = true;
                g_blocks[i].is_fresh = false;
            }
            
            // Calculate partial fill ratio
            UpdatePartialFill(i, current_high, current_low);
            
            // Check for block break
            CheckBlockBreak(i, current_high, current_low);
            
            // Send alert if enabled
            if(EnableAlerts) {
                SendBlockAlert(i);
            }
        }
        
        // Update visual representation
        UpdateBlockVisual(i);
    }
}

//+------------------------------------------------------------------+
//| Update partial fill ratio                                        |
//+------------------------------------------------------------------+
void UpdatePartialFill(int block_index, double current_high, double current_low) {
    double block_size = g_blocks[block_index].high_price - g_blocks[block_index].low_price;
    double filled_size = 0;
    
    if(g_blocks[block_index].is_bullish) {
        // For bullish blocks, measure how much of the upper part is consumed
        if(current_low < g_blocks[block_index].high_price) {
            filled_size = g_blocks[block_index].high_price - MathMax(current_low, g_blocks[block_index].low_price);
        }
    } else {
        // For bearish blocks, measure how much of the lower part is consumed
        if(current_high > g_blocks[block_index].low_price) {
            filled_size = MathMin(current_high, g_blocks[block_index].high_price) - g_blocks[block_index].low_price;
        }
    }
    
    g_blocks[block_index].partial_fill_ratio = (block_size > 0) ? filled_size / block_size : 0;
    g_blocks[block_index].partial_fill_ratio = MathMax(0, MathMin(1, g_blocks[block_index].partial_fill_ratio));
}

//+------------------------------------------------------------------+
//| Check if block is broken                                         |
//+------------------------------------------------------------------+
void CheckBlockBreak(int block_index, double current_high, double current_low) {
    bool is_broken = false;
    
    if(g_blocks[block_index].is_bullish) {
        // Bullish block broken when price closes below the low with momentum
        is_broken = (current_low < g_blocks[block_index].low_price - g_atr_value * 0.2);
    } else {
        // Bearish block broken when price closes above the high with momentum
        is_broken = (current_high > g_blocks[block_index].high_price + g_atr_value * 0.2);
    }
    
    if(is_broken) {
        g_blocks[block_index].is_broken = true;
        Print(StringFormat("Order Block broken: %s", g_blocks[block_index].obj_name));
    }
}

//+------------------------------------------------------------------+
//| Send block alert                                                 |
//+------------------------------------------------------------------+
void SendBlockAlert(int block_index) {
    static datetime last_alert_time = 0;
    if(TimeCurrent() - last_alert_time < 300) return; // Limit alerts to once per 5 minutes
    
    string alert_message = StringFormat("Price approaching %s Order Block at %.5f-%.5f (%s)", 
        g_blocks[block_index].is_bullish ? "Bullish" : "Bearish",
        g_blocks[block_index].low_price,
        g_blocks[block_index].high_price,
        EnumToString(g_blocks[block_index].timeframe));
    
    Alert(alert_message);
    last_alert_time = TimeCurrent();
}

//+------------------------------------------------------------------+
//| Update block visual representation                               |
//+------------------------------------------------------------------+
void UpdateBlockVisual(int block_index) {
    string obj_name = g_blocks[block_index].obj_name;
    
    // Update transparency based on age, strength, and partial fill
    int transparency = CalculateTransparency(block_index);
    
    color block_color = g_blocks[block_index].is_bullish ? BullishBlockColor : BearishBlockColor;
    switch(g_blocks[block_index].timeframe) {
        case PERIOD_H1: block_color = H1BlockColor; break;
        case PERIOD_H4: block_color = H4BlockColor; break;
        case PERIOD_D1: block_color = D1BlockColor; break;
    }
    
    if(ObjectFind(0, obj_name) >= 0) {
        ObjectSetInteger(0, obj_name, OBJPROP_BGCOLOR, ColorWithTransparency(block_color, transparency));
        
        // Update label if it exists
        string label_name = obj_name + "_label";
        if(ObjectFind(0, label_name) >= 0 && ShowBlockLabels) {
            string label_text = StringFormat("%s %s\nS:%.1f T:%d %s\nFill:%.0f%%", 
                EnumToString(g_blocks[block_index].timeframe),
                g_blocks[block_index].is_bullish ? "Bull" : "Bear",
                g_blocks[block_index].strength,
                g_blocks[block_index].touches,
                g_blocks[block_index].is_fresh ? "Fresh" : "Used",
                g_blocks[block_index].partial_fill_ratio * 100);
            
            ObjectSetString(0, label_name, OBJPROP_TEXT, label_text);
        }
    }
}

//+------------------------------------------------------------------+
//| Calculate transparency based on block properties                 |
//+------------------------------------------------------------------+
int CalculateTransparency(int block_index) {
    int base_transparency = BlockTransparency;
    
    // Increase transparency based on age
    int age_hours = (int)((TimeCurrent() - g_blocks[block_index].creation_time) / 3600);
    int age_penalty = (age_hours * 30) / MaxBlockAge; // Up to 30% more transparent
    
    // Decrease transparency based on strength (stronger blocks more opaque)
    int strength_bonus = (int)(g_blocks[block_index].strength * 10); // Up to 10% less transparent
    
    // Increase transparency based on partial fill
    int fill_penalty = (int)(g_blocks[block_index].partial_fill_ratio * 40); // Up to 40% more transparent
    
    int final_transparency = base_transparency + age_penalty + fill_penalty - strength_bonus;
    return MathMax(0, MathMin(100, final_transparency));
}

//+------------------------------------------------------------------+
//| Create color with transparency                                   |
//+------------------------------------------------------------------+
color ColorWithTransparency(color base_color, int transparency) {
    int r = (base_color) & 0xFF;
    int g = (base_color >> 8) & 0xFF;
    int b = (base_color >> 16) & 0xFF;
    int a = 255 - (transparency * 255 / 100);
    
    return (color)((a << 24) | (b << 16) | (g << 8) | r);
}

//+------------------------------------------------------------------+
//| Check for fresh signal at order block                           |
//+------------------------------------------------------------------+
bool CheckForFreshSignal(int block_index, double current_close) {
    // Only generate signals for fresh blocks or blocks that haven't been signaled
    if(!g_blocks[block_index].is_fresh && g_blocks[block_index].signal_sent) {
        return false;
    }
    
    // Check if price is approaching the block from the correct direction
    bool valid_approach = false;
    
    if(g_blocks[block_index].is_bullish) {
        // For bullish blocks, price should approach from above
        double prev_close = iClose(_Symbol, _Period, 1);
        valid_approach = (prev_close > g_blocks[block_index].high_price && 
                         current_close <= g_blocks[block_index].high_price);
    } else {
        // For bearish blocks, price should approach from below  
        double prev_close = iClose(_Symbol, _Period, 1);
        valid_approach = (prev_close < g_blocks[block_index].low_price && 
                         current_close >= g_blocks[block_index].low_price);
    }
    
    return valid_approach;
}

//+------------------------------------------------------------------+
//| Generate trading signal                                          |
//+------------------------------------------------------------------+
void GenerateTradingSignal(int block_index, double current_close) {
    datetime signal_time = iTime(_Symbol, _Period, 0);
    
    if(g_blocks[block_index].is_bullish) {
        // Generate BUY signal
        if(ShowBuySignals) {
            BuySignalBuffer[0] = g_blocks[block_index].low_price - g_atr_value * 0.5;
        }
        
        // Send alerts
        if(SendPopupAlerts) {
            Alert(StringFormat("🟢 BUY SIGNAL at %s Order Block! Price: %.5f [%s TF]", 
                  g_blocks[block_index].is_bullish ? "Bullish" : "Bearish",
                  current_close, 
                  EnumToString(g_blocks[block_index].timeframe)));
        }
        
        if(PlaySoundAlerts) {
            PlaySound("alert.wav");
        }
        
        // Execute trade if auto-trading is enabled
        if(AutoTrade) {
            ExecuteBuyOrder(block_index);
        }
        
        Print(StringFormat("🟢 BUY SIGNAL generated at Order Block: %s, Price: %.5f", 
              g_blocks[block_index].obj_name, current_close));
              
    } else {
        // Generate SELL signal
        if(ShowSellSignals) {
            SellSignalBuffer[0] = g_blocks[block_index].high_price + g_atr_value * 0.5;
        }
        
        // Send alerts
        if(SendPopupAlerts) {
            Alert(StringFormat("🔴 SELL SIGNAL at %s Order Block! Price: %.5f [%s TF]", 
                  g_blocks[block_index].is_bullish ? "Bullish" : "Bearish",
                  current_close, 
                  EnumToString(g_blocks[block_index].timeframe)));
        }
        
        if(PlaySoundAlerts) {
            PlaySound("alert2.wav");
        }
        
        // Execute trade if auto-trading is enabled
        if(AutoTrade) {
            ExecuteSellOrder(block_index);
        }
        
        Print(StringFormat("🔴 SELL SIGNAL generated at Order Block: %s, Price: %.5f", 
              g_blocks[block_index].obj_name, current_close));
    }
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckTradingSignals() {
    if(!AutoTrade) return;
    if(PositionsTotal() >= MaxOpenTrades) return;
    if(AccountInfoDouble(ACCOUNT_EQUITY) < MinEquity) return;
    
    // Trading signals are now handled in GenerateTradingSignal function
    // This function is kept for compatibility but main logic moved to UpdateOrderBlocks
}

//+------------------------------------------------------------------+
//| Execute buy order                                                |
//+------------------------------------------------------------------+
void ExecuteBuyOrder(int block_index) {
    // Check if trading is allowed
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED)) {
        Print("Trading is not allowed in the terminal");
        return;
    }
    
    if(!MQLInfoInteger(MQL_TRADE_ALLOWED)) {
        Print("Trading is not allowed for this EA");
        return;
    }
    
    // Get current prices
    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double stop_loss = g_blocks[block_index].low_price - g_atr_value * 0.5;
    double take_profit = entry_price + (entry_price - stop_loss) * 2.0; // 1:2 R:R
    
    // Normalize prices
    entry_price = NormalizeDouble(entry_price, _Digits);
    stop_loss = NormalizeDouble(stop_loss, _Digits);
    take_profit = NormalizeDouble(take_profit, _Digits);
    
    // Validate stop loss and take profit
    double min_stop_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * _Point;
    if((entry_price - stop_loss) < min_stop_level) {
        stop_loss = entry_price - min_stop_level;
        stop_loss = NormalizeDouble(stop_loss, _Digits);
    }
    
    if((take_profit - entry_price) < min_stop_level) {
        take_profit = entry_price + min_stop_level;
        take_profit = NormalizeDouble(take_profit, _Digits);
    }
    
    // Calculate position size based on risk
    double risk_amount = AccountInfoDouble(ACCOUNT_EQUITY) * RiskPercent / 100;
    double stop_distance = entry_price - stop_loss;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    double position_size = LotSize;
    if(stop_distance > 0 && tick_value > 0 && tick_size > 0) {
        position_size = risk_amount / (stop_distance / tick_size * tick_value);
    }
    
    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    position_size = MathMax(min_lot, MathMin(max_lot, position_size));
    position_size = NormalizeDouble(position_size / lot_step, 0) * lot_step;
    
    // Execute the trade
    trade.SetExpertMagicNumber(123456);
    trade.SetDeviationInPoints(10);
    
    if(trade.Buy(position_size, _Symbol, entry_price, stop_loss, take_profit, 
                StringFormat("OB Buy %s", g_blocks[block_index].obj_name))) {
        Print(StringFormat("✅ BUY ORDER EXECUTED: Lots=%.2f, Entry=%.5f, SL=%.5f, TP=%.5f", 
              position_size, entry_price, stop_loss, take_profit));
        g_blocks[block_index].is_fresh = false;
    } else {
        Print(StringFormat("❌ BUY ORDER FAILED: %d - %s", trade.ResultRetcode(), trade.ResultRetcodeDescription()));
    }
}

//+------------------------------------------------------------------+
//| Execute sell order                                               |
//+------------------------------------------------------------------+
void ExecuteSellOrder(int block_index) {
    // Check if trading is allowed
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED)) {
        Print("Trading is not allowed in the terminal");
        return;
    }
    
    if(!MQLInfoInteger(MQL_TRADE_ALLOWED)) {
        Print("Trading is not allowed for this EA");
        return;
    }
    
    // Get current prices
    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double stop_loss = g_blocks[block_index].high_price + g_atr_value * 0.5;
    double take_profit = entry_price - (stop_loss - entry_price) * 2.0; // 1:2 R:R
    
    // Normalize prices
    entry_price = NormalizeDouble(entry_price, _Digits);
    stop_loss = NormalizeDouble(stop_loss, _Digits);
    take_profit = NormalizeDouble(take_profit, _Digits);
    
    // Validate stop loss and take profit
    double min_stop_level = SymbolInfoInteger(_Symbol, SYMBOL_TRADE_STOPS_LEVEL) * _Point;
    if((stop_loss - entry_price) < min_stop_level) {
        stop_loss = entry_price + min_stop_level;
        stop_loss = NormalizeDouble(stop_loss, _Digits);
    }
    
    if((entry_price - take_profit) < min_stop_level) {
        take_profit = entry_price - min_stop_level;
        take_profit = NormalizeDouble(take_profit, _Digits);
    }
    
    // Calculate position size based on risk
    double risk_amount = AccountInfoDouble(ACCOUNT_EQUITY) * RiskPercent / 100;
    double stop_distance = stop_loss - entry_price;
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    
    double position_size = LotSize;
    if(stop_distance > 0 && tick_value > 0 && tick_size > 0) {
        position_size = risk_amount / (stop_distance / tick_size * tick_value);
    }
    
    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    
    position_size = MathMax(min_lot, MathMin(max_lot, position_size));
    position_size = NormalizeDouble(position_size / lot_step, 0) * lot_step;
    
    // Execute the trade
    trade.SetExpertMagicNumber(123456);
    trade.SetDeviationInPoints(10);
    
    if(trade.Sell(position_size, _Symbol, entry_price, stop_loss, take_profit, 
                 StringFormat("OB Sell %s", g_blocks[block_index].obj_name))) {
        Print(StringFormat("✅ SELL ORDER EXECUTED: Lots=%.2f, Entry=%.5f, SL=%.5f, TP=%.5f", 
              position_size, entry_price, stop_loss, take_profit));
        g_blocks[block_index].is_fresh = false;
    } else {
        Print(StringFormat("❌ SELL ORDER FAILED: %d - %s", trade.ResultRetcode(), trade.ResultRetcodeDescription()));
    }
}

//+------------------------------------------------------------------+
//| Clean up old and invalid order blocks                           |
//+------------------------------------------------------------------+
void CleanupOrderBlocks() {
    for(int i = g_block_count - 1; i >= 0; i--) {
        bool should_remove = false;
        
        // Remove if too old
        int age_hours = (int)((TimeCurrent() - g_blocks[i].creation_time) / 3600);
        if(age_hours > MaxBlockAge) {
            should_remove = true;
        }
        
        // Remove if broken with strong momentum
        if(g_blocks[i].is_broken && g_blocks[i].partial_fill_ratio > 0.8) {
            should_remove = true;
        }
        
        if(should_remove) {
            // Remove visual objects
            ObjectDelete(0, g_blocks[i].obj_name);
            ObjectDelete(0, g_blocks[i].obj_name + "_label");
            
            // Remove from array
            for(int j = i; j < g_block_count - 1; j++) {
                g_blocks[j] = g_blocks[j + 1];
            }
            g_block_count--;
            ArrayResize(g_blocks, g_block_count);
        }
    }
    
    ChartRedraw();
}

// Function to generate a standardized trading signal for aggregation
TradingSignal GetTradingSignal() {
    TradingSignal signal;
    signal.signal_type = SIGNAL_TYPE_HOLD;
    signal.confidence_level = 0.0;
    signal.stop_loss = 0.0;
    signal.take_profit = 0.0;
    StringToCharArray("", signal.parameters, 0, 255);
    StringToCharArray("Advanced Order Block EA", signal.strategy_name, 0, 63);

    // Example: Use the most recent fresh, unbroken order block as a signal
    for (int i = 0; i < g_block_count; i++) {
        OrderBlock block = g_blocks[i];
        if (block.is_fresh && !block.is_broken) {
            if (block.is_bullish) {
                signal = CreateTradingSignal(SIGNAL_TYPE_BUY, 
                    block.strength > 0 ? block.strength : 0.7,
                    block.low_price - (block.high_price - block.low_price) * 0.5,
                    block.high_price + (block.high_price - block.low_price) * 2,
                    "OrderBlock_Bullish_" + IntegerToString(i),
                    "Advanced Order Block EA");
                break;
            } else {
                signal = CreateTradingSignal(SIGNAL_TYPE_SELL, 
                    block.strength > 0 ? block.strength : 0.7,
                    block.high_price + (block.high_price - block.low_price) * 0.5,
                    block.low_price - (block.high_price - block.low_price) * 2,
                    "OrderBlock_Bearish_" + IntegerToString(i),
                    "Advanced Order Block EA");
                break;
            }
        }
    }

    // Write signal to file for Misape Bot to read
    string filename = GetSignalFilePath(CharArrayToString(signal.strategy_name));
    if(WriteSignalToFile(filename, signal, CharArrayToString(signal.strategy_name))) {
        Print("Advanced Order Block signal written to file: ", filename);
    } else {
        Print("Failed to write Advanced Order Block signal to file");
    }

    return signal;
}
