//+------------------------------------------------------------------+
//|                                      Consolidated_Misape_Bot.mq5 |
//|                    Consolidated Multi-Strategy Trading System     |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "2.00"
#property description "Consolidated multi-strategy trading system with consensus-based signal aggregation"
#property strict

#include <Trade/Trade.mqh>

//+------------------------------------------------------------------+
//| <PERSON><PERSON>MS AND STRUCTURES                                             |
//+------------------------------------------------------------------+

//--- Trading Signal Type Enum
enum ENUM_SIGNAL_TYPE {
    SIGNAL_TYPE_HOLD = 0,  // No signal
    SIGNAL_TYPE_BUY = 1,   // Buy signal
    SIGNAL_TYPE_SELL = 2   // Sell signal
};

//--- Strategy Type Enum
enum ENUM_STRATEGY_TYPE {
    STRATEGY_ORDER_BLOCK = 0,
    STRATEGY_FAIR_VALUE_GAP = 1,
    STRATEGY_MARKET_STRUCTURE = 2,
    STRATEGY_RANGE_BREAKOUT = 3,
    STRATEGY_SUPPORT_RESISTANCE = 4,
    STRATEGY_CHART_PATTERN = 5
};

//--- Trading Signal Structure
struct TradingSignal {
    ENUM_SIGNAL_TYPE signal_type;    // Type of signal (HOLD, BUY, SELL)
    double confidence_level;          // Confidence level (0.0 to 1.0)
    double stop_loss;                 // Stop loss price
    double take_profit;               // Take profit price
    string parameters;                // Additional parameters
    string strategy_name;             // Name of the strategy that generated the signal
    datetime timestamp;               // When signal was generated
    bool is_valid;                    // Whether signal is still valid
};

//--- Order Block Structure
struct OrderBlock {
    datetime time_created;
    double high_price;
    double low_price;
    double open_price;
    double close_price;
    ENUM_TIMEFRAMES timeframe;
    bool is_bullish;
    bool is_fresh;
    bool is_broken;
    int touches;
    datetime last_touch;
    double strength;
    string obj_name;
    bool signal_sent;
    double partial_fill_ratio;
};

//--- Strategy Status Structure
struct StrategyStatus {
    string name;
    TradingSignal last_signal;
    datetime last_updated;
    bool is_active;
    color status_color;
};

//+------------------------------------------------------------------+
//| INPUT PARAMETERS                                                 |
//+------------------------------------------------------------------+

input group "=== Master Trading Settings ==="
input long MagicNumber = 789012;           // Magic number for trades
input bool EnableTrading = true;           // Master switch to enable/disable trading
input double DefaultLotSize = 0.01;        // Default lot size
input double RiskPercent = 2.0;            // Risk percentage per trade
input int MaxOpenTrades = 3;               // Maximum open trades
input double MinEquity = 100.0;            // Minimum account equity to trade

input group "=== Consensus Settings ==="
input int MinSignalConsensus = 2;          // Minimum number of signals required for consensus
input double MinConfidenceThreshold = 0.65; // Minimum average confidence level for consensus trade
input int SignalExpirySeconds = 300;       // Signal expiry time in seconds (5 minutes)

input group "=== Risk Management ==="
input double ATR_Multiplier_SL = 2.0;      // ATR multiplier for stop loss
input double ATR_Multiplier_TP = 3.0;      // ATR multiplier for take profit

input group "=== Dashboard Settings ==="
input bool EnableDashboard = true;         // Enable/disable the visual dashboard

input group "=== Debug Settings ==="
input bool EnableDebugLogging = true;      // Enable detailed debug logging
input bool ShowSignalDetails = true;       // Show individual strategy signals in log

input group "=== Order Block Strategy ==="
input bool EnableOrderBlock = true;        // Enable Order Block strategy
input int OB_SwingLength = 5;              // Swing detection length
input bool OB_ShowH1Blocks = true;         // Show H1 timeframe blocks
input bool OB_ShowH4Blocks = true;         // Show H4 timeframe blocks
input bool OB_ShowD1Blocks = true;         // Show D1 timeframe blocks
input double OB_MinBlockStrength = 1.0;    // Minimum block strength

input group "=== Fair Value Gap Strategy ==="
input bool EnableFairValueGap = true;      // Enable Fair Value Gap strategy
input double FVG_MinGapSize = 10.0;        // Minimum gap size in points
input double FVG_MaxMiddleCandleRatio = 0.3; // Maximum middle candle ratio

input group "=== Market Structure Strategy ==="
input bool EnableMarketStructure = true;   // Enable Market Structure strategy
input int MS_SwingPeriod = 10;             // Swing detection period

input group "=== Range Breakout Strategy ==="
input bool EnableRangeBreakout = true;     // Enable Range Breakout strategy
input int RB_RangePeriod = 24;             // Range calculation period (hours)
input int RB_ValidBreakStartHour = 6;      // Valid breakout start hour
input int RB_ValidBreakEndHour = 13;       // Valid breakout end hour

input group "=== Support/Resistance Strategy ==="
input bool EnableSupportResistance = true; // Enable Support/Resistance strategy
input int SR_LookbackPeriod = 100;         // Lookback period for S/R detection
input double SR_LevelTolerance = 10.0;     // Level tolerance in points

input group "=== Chart Pattern Strategy ==="
input bool EnableChartPattern = true;      // Enable Chart Pattern strategy
input int CP_SwingLength = 5;              // Swing length for pattern detection
input double CP_MinPatternSize = 20.0;     // Minimum pattern size in points
input int CP_RSI_Period = 14;              // RSI period for confirmation
input double CP_RSI_Overbought = 70.0;     // RSI overbought level
input double CP_RSI_Oversold = 30.0;       // RSI oversold level
input bool CP_ShowPatterns = true;         // Show pattern drawings on chart

//+------------------------------------------------------------------+
//| GLOBAL VARIABLES                                                 |
//+------------------------------------------------------------------+

CTrade trade;
StrategyStatus g_strategies[6];
TradingSignal g_signals[6];
OrderBlock g_order_blocks[];
int g_block_count = 0;
datetime g_last_bar_time = 0;
double g_atr_value = 0;
int g_atr_handle = INVALID_HANDLE;
int g_rsi_handle = INVALID_HANDLE;

// Range Breakout variables
double g_daily_high = 0;
double g_daily_low = 0;
bool g_range_established = false;
bool g_range_broken = false;

// Support/Resistance variables
double g_resistance_levels[2];
double g_support_levels[2];

// Dashboard constants
#define DASHBOARD_PREFIX "ConsolidatedBot_"
#define CARD_WIDTH 220
#define CARD_HEIGHT 100
#define SPACING_X 15
#define SPACING_Y 15
#define START_X 25
#define START_Y 60

// Color definitions
#define COLOR_BACKGROUND clrBlack
#define COLOR_CARD_BG 0x2B2B2B
#define COLOR_CARD_BORDER 0x4A4A4A
#define COLOR_TEXT_HEADER clrWhite
#define COLOR_TEXT_NORMAL clrSilver
#define COLOR_TEXT_ACCENT clrDodgerBlue
#define COLOR_BUY clrSeaGreen
#define COLOR_SELL clrCrimson
#define COLOR_HOLD clrGoldenrod
#define COLOR_PROFIT clrLimeGreen
#define COLOR_LOSS clrFireBrick

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    Print("=== Consolidated Misape Bot Initializing ===");
    
    // Initialize trade object
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(10);
    
    // Initialize ATR handle
    g_atr_handle = iATR(_Symbol, _Period, 14);
    if(g_atr_handle == INVALID_HANDLE) {
        Print("Failed to create ATR indicator handle");
        return INIT_FAILED;
    }

    // Initialize RSI handle
    g_rsi_handle = iRSI(_Symbol, _Period, CP_RSI_Period, PRICE_CLOSE);
    if(g_rsi_handle == INVALID_HANDLE) {
        Print("Failed to create RSI indicator handle");
        return INIT_FAILED;
    }
    
    // Initialize strategy statuses
    InitializeStrategies();
    
    // Initialize arrays
    ArrayResize(g_order_blocks, 100);
    g_block_count = 0;
    
    // Create dashboard if enabled
    if(EnableDashboard) {
        CreateDashboard();
    }
    
    Print("=== Consolidated Misape Bot Initialized Successfully ===");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Clean up indicators
    if(g_atr_handle != INVALID_HANDLE) {
        IndicatorRelease(g_atr_handle);
    }
    if(g_rsi_handle != INVALID_HANDLE) {
        IndicatorRelease(g_rsi_handle);
    }
    
    // Clean up dashboard objects
    if(EnableDashboard) {
        ObjectsDeleteAll(0, DASHBOARD_PREFIX);
    }
    
    // Clean up order block objects
    for(int i = 0; i < g_block_count; i++) {
        ObjectDelete(0, g_order_blocks[i].obj_name);
        ObjectDelete(0, g_order_blocks[i].obj_name + "_label");
    }
    
    Print("=== Consolidated Misape Bot Deinitialized ===");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // Update ATR value
    UpdateATR();
    
    // Check for new bar
    datetime current_time = iTime(_Symbol, _Period, 0);
    if(current_time != g_last_bar_time) {
        g_last_bar_time = current_time;
        OnNewBar();
    }
    
    // Update dashboard
    if(EnableDashboard) {
        UpdateDashboard();
    }
    
    // Execute consensus trading logic
    if(EnableTrading) {
        ExecuteConsensusTrading();
    }
}

//+------------------------------------------------------------------+
//| New bar event handler                                            |
//+------------------------------------------------------------------+
void OnNewBar() {
    if(EnableDebugLogging) {
        Print("=== OnNewBar() - New Bar Detected ===");
        Print("Time: ", TimeToString(TimeCurrent()));
        Print("Symbol: ", _Symbol, " Period: ", EnumToString((ENUM_TIMEFRAMES)_Period));
    }

    // Update ATR value
    UpdateATR();
    if(EnableDebugLogging) Print("ATR Updated: ", g_atr_value);

    // Clear old signals
    ClearExpiredSignals();

    // Run all enabled strategies
    if(EnableOrderBlock) {
        if(EnableDebugLogging) Print("Running Order Block Strategy...");
        RunOrderBlockStrategy();
    }
    if(EnableFairValueGap) {
        if(EnableDebugLogging) Print("Running Fair Value Gap Strategy...");
        RunFairValueGapStrategy();
    }
    if(EnableMarketStructure) {
        if(EnableDebugLogging) Print("Running Market Structure Strategy...");
        RunMarketStructureStrategy();
    }
    if(EnableRangeBreakout) {
        if(EnableDebugLogging) Print("Running Range Breakout Strategy...");
        RunRangeBreakoutStrategy();
    }
    if(EnableSupportResistance) {
        if(EnableDebugLogging) Print("Running Support/Resistance Strategy...");
        RunSupportResistanceStrategy();
    }
    if(EnableChartPattern) {
        if(EnableDebugLogging) Print("Running Chart Pattern Strategy...");
        RunChartPatternStrategy();
    }

    if(EnableDebugLogging) Print("=== OnNewBar() Complete ===");
}

//+------------------------------------------------------------------+
//| UTILITY FUNCTIONS                                                |
//+------------------------------------------------------------------+



//+------------------------------------------------------------------+
//| Initialize strategy statuses                                     |
//+------------------------------------------------------------------+
void InitializeStrategies() {
    g_strategies[0].name = "Order Block";
    g_strategies[1].name = "Fair Value Gap";
    g_strategies[2].name = "Market Structure";
    g_strategies[3].name = "Range Breakout";
    g_strategies[4].name = "Support/Resistance";
    g_strategies[5].name = "Chart Pattern";

    for(int i = 0; i < 6; i++) {
        g_strategies[i].is_active = false;
        g_strategies[i].status_color = COLOR_HOLD;
        g_strategies[i].last_updated = 0;
        g_strategies[i].last_signal.signal_type = SIGNAL_TYPE_HOLD;
        g_strategies[i].last_signal.confidence_level = 0.0;
        g_strategies[i].last_signal.is_valid = false;
    }
}

//+------------------------------------------------------------------+
//| Update ATR value                                                 |
//+------------------------------------------------------------------+
void UpdateATR() {
    if(g_atr_handle != INVALID_HANDLE) {
        double atr_buffer[1];
        if(CopyBuffer(g_atr_handle, 0, 1, 1, atr_buffer) > 0) {
            g_atr_value = atr_buffer[0];
        }
    }
}

//+------------------------------------------------------------------+
//| Clear expired signals                                            |
//+------------------------------------------------------------------+
void ClearExpiredSignals() {
    datetime current_time = TimeCurrent();
    for(int i = 0; i < 6; i++) {
        if(g_strategies[i].last_signal.is_valid) {
            if(current_time - g_strategies[i].last_signal.timestamp > SignalExpirySeconds) {
                g_strategies[i].last_signal.is_valid = false;
                g_strategies[i].last_signal.signal_type = SIGNAL_TYPE_HOLD;
                g_strategies[i].status_color = COLOR_HOLD;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Create trading signal                                            |
//+------------------------------------------------------------------+
TradingSignal CreateTradingSignal(ENUM_SIGNAL_TYPE type, double confidence,
                                 double sl, double tp, string params, string strategy) {
    TradingSignal signal;
    signal.signal_type = type;
    signal.confidence_level = confidence;
    signal.stop_loss = sl;
    signal.take_profit = tp;
    signal.parameters = params;
    signal.strategy_name = strategy;
    signal.timestamp = TimeCurrent();
    signal.is_valid = true;

    return signal;
}

//+------------------------------------------------------------------+
//| Get signal type string                                           |
//+------------------------------------------------------------------+
string GetSignalTypeString(ENUM_SIGNAL_TYPE signal_type) {
    switch (signal_type) {
        case SIGNAL_TYPE_BUY:  return "BUY";
        case SIGNAL_TYPE_SELL: return "SELL";
        case SIGNAL_TYPE_HOLD: return "HOLD";
        default:               return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Update strategy signal                                           |
//+------------------------------------------------------------------+
void UpdateStrategySignal(ENUM_STRATEGY_TYPE strategy_type, TradingSignal &signal) {
    int index = (int)strategy_type;
    if(index >= 0 && index < 6) {
        g_strategies[index].last_signal = signal;
        g_strategies[index].last_updated = TimeCurrent();
        g_strategies[index].is_active = signal.is_valid;

        // Update status color based on signal type
        switch(signal.signal_type) {
            case SIGNAL_TYPE_BUY:  g_strategies[index].status_color = COLOR_BUY; break;
            case SIGNAL_TYPE_SELL: g_strategies[index].status_color = COLOR_SELL; break;
            default:               g_strategies[index].status_color = COLOR_HOLD; break;
        }
    }
}

//+------------------------------------------------------------------+
//| CONSENSUS TRADING LOGIC                                          |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Execute consensus trading logic                                  |
//+------------------------------------------------------------------+
void ExecuteConsensusTrading() {
    if(EnableDebugLogging) {
        Print("=== ExecuteConsensusTrading() Called ===");
        Print("EnableTrading: ", EnableTrading);
        Print("Current Positions: ", PositionsTotal(), " / Max: ", MaxOpenTrades);
        Print("Account Equity: $", AccountInfoDouble(ACCOUNT_EQUITY), " / Min Required: $", MinEquity);
    }

    if(!EnableTrading) {
        if(EnableDebugLogging) Print("TRADING DISABLED - EnableTrading = false");
        return;
    }
    if(PositionsTotal() >= MaxOpenTrades) {
        if(EnableDebugLogging) Print("MAX TRADES REACHED - Current: ", PositionsTotal(), " Max: ", MaxOpenTrades);
        return;
    }
    if(AccountInfoDouble(ACCOUNT_EQUITY) < MinEquity) {
        if(EnableDebugLogging) Print("INSUFFICIENT EQUITY - Current: $", AccountInfoDouble(ACCOUNT_EQUITY), " Required: $", MinEquity);
        return;
    }

    // Count valid signals by type
    int buy_signals = 0, sell_signals = 0;
    double buy_confidence_sum = 0.0, sell_confidence_sum = 0.0;
    double avg_sl_buy = 0.0, avg_tp_buy = 0.0;
    double avg_sl_sell = 0.0, avg_tp_sell = 0.0;

    if(EnableDebugLogging) {
        Print("=== Analyzing Strategy Signals ===");
    }

    for(int i = 0; i < 6; i++) {
        if(EnableDebugLogging && ShowSignalDetails) {
            Print("Strategy ", i, " (", g_strategies[i].name, "):");
            Print("  - Signal Valid: ", g_strategies[i].last_signal.is_valid);
            Print("  - Signal Type: ", GetSignalTypeString(g_strategies[i].last_signal.signal_type));
            Print("  - Confidence: ", g_strategies[i].last_signal.confidence_level, " (Required: ", MinConfidenceThreshold, ")");
            Print("  - Age: ", TimeCurrent() - g_strategies[i].last_signal.timestamp, " seconds");
        }

        if(g_strategies[i].last_signal.is_valid &&
           g_strategies[i].last_signal.confidence_level >= MinConfidenceThreshold) {

            if(g_strategies[i].last_signal.signal_type == SIGNAL_TYPE_BUY) {
                buy_signals++;
                buy_confidence_sum += g_strategies[i].last_signal.confidence_level;
                avg_sl_buy += g_strategies[i].last_signal.stop_loss;
                avg_tp_buy += g_strategies[i].last_signal.take_profit;
                if(EnableDebugLogging) Print("Valid BUY signal from ", g_strategies[i].name, " (Confidence: ", g_strategies[i].last_signal.confidence_level, ")");
            }
            else if(g_strategies[i].last_signal.signal_type == SIGNAL_TYPE_SELL) {
                sell_signals++;
                sell_confidence_sum += g_strategies[i].last_signal.confidence_level;
                avg_sl_sell += g_strategies[i].last_signal.stop_loss;
                avg_tp_sell += g_strategies[i].last_signal.take_profit;
                if(EnableDebugLogging) Print("Valid SELL signal from ", g_strategies[i].name, " (Confidence: ", g_strategies[i].last_signal.confidence_level, ")");
            }
        }
    }

    if(EnableDebugLogging) {
        Print("=== Signal Summary ===");
        Print("BUY Signals: ", buy_signals, " (Required: ", MinSignalConsensus, ")");
        Print("SELL Signals: ", sell_signals, " (Required: ", MinSignalConsensus, ")");
        if(buy_signals > 0) Print("Average BUY Confidence: ", buy_confidence_sum / buy_signals, " (Required: ", MinConfidenceThreshold, ")");
        if(sell_signals > 0) Print("Average SELL Confidence: ", sell_confidence_sum / sell_signals, " (Required: ", MinConfidenceThreshold, ")");
    }

    // Check for BUY consensus
    if(buy_signals >= MinSignalConsensus) {
        double avg_confidence = buy_confidence_sum / buy_signals;
        if(EnableDebugLogging) Print("BUY CONSENSUS REACHED - Signals: ", buy_signals, ", Avg Confidence: ", avg_confidence);
        if(avg_confidence >= MinConfidenceThreshold) {
            avg_sl_buy = buy_signals > 0 ? avg_sl_buy / buy_signals : 0;
            avg_tp_buy = buy_signals > 0 ? avg_tp_buy / buy_signals : 0;
            if(EnableDebugLogging) Print("EXECUTING BUY TRADE - Confidence: ", avg_confidence, ", SL: ", avg_sl_buy, ", TP: ", avg_tp_buy);
            ExecuteTrade(SIGNAL_TYPE_BUY, avg_confidence, avg_sl_buy, avg_tp_buy, "Consensus BUY");
            return;
        } else {
            if(EnableDebugLogging) Print("BUY CONSENSUS REJECTED - Confidence too low: ", avg_confidence, " < ", MinConfidenceThreshold);
        }
    } else {
        if(EnableDebugLogging && buy_signals > 0) Print("BUY CONSENSUS NOT REACHED - Signals: ", buy_signals, " < ", MinSignalConsensus);
    }

    // Check for SELL consensus
    if(sell_signals >= MinSignalConsensus) {
        double avg_confidence = sell_confidence_sum / sell_signals;
        if(EnableDebugLogging) Print("SELL CONSENSUS REACHED - Signals: ", sell_signals, ", Avg Confidence: ", avg_confidence);
        if(avg_confidence >= MinConfidenceThreshold) {
            avg_sl_sell = sell_signals > 0 ? avg_sl_sell / sell_signals : 0;
            avg_tp_sell = sell_signals > 0 ? avg_tp_sell / sell_signals : 0;
            if(EnableDebugLogging) Print("EXECUTING SELL TRADE - Confidence: ", avg_confidence, ", SL: ", avg_sl_sell, ", TP: ", avg_tp_sell);
            ExecuteTrade(SIGNAL_TYPE_SELL, avg_confidence, avg_sl_sell, avg_tp_sell, "Consensus SELL");
            return;
        } else {
            if(EnableDebugLogging) Print("SELL CONSENSUS REJECTED - Confidence too low: ", avg_confidence, " < ", MinConfidenceThreshold);
        }
    } else {
        if(EnableDebugLogging && sell_signals > 0) Print("SELL CONSENSUS NOT REACHED - Signals: ", sell_signals, " < ", MinSignalConsensus);
    }

    if(EnableDebugLogging && buy_signals == 0 && sell_signals == 0) {
        Print("NO VALID SIGNALS FOUND - All strategies inactive or below confidence threshold");
    }
}

//+------------------------------------------------------------------+
//| Execute trade based on consensus signal                         |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_SIGNAL_TYPE signal_type, double confidence, double sl, double tp, string comment) {
    if(EnableDebugLogging) {
        Print("=== ExecuteTrade() Called ===");
        Print("Signal Type: ", GetSignalTypeString(signal_type));
        Print("Confidence: ", confidence);
        Print("Stop Loss: ", sl);
        Print("Take Profit: ", tp);
        Print("Comment: ", comment);
    }

    double lot_size = CalculateLotSize();
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    if(EnableDebugLogging) {
        Print("Calculated Lot Size: ", lot_size);
        Print("Current Ask: ", ask);
        Print("Current Bid: ", bid);
    }

    bool result = false;

    if(signal_type == SIGNAL_TYPE_BUY) {
        // Validate SL and TP for BUY
        if(sl > 0 && sl >= ask) sl = ask - g_atr_value;
        if(tp > 0 && tp <= ask) tp = ask + g_atr_value * 2;

        result = trade.Buy(lot_size, _Symbol, ask, sl, tp, comment);
        if(result) {
            Print("BUY order executed: Lot=", lot_size, " Price=", ask, " SL=", sl, " TP=", tp, " Confidence=", confidence);
        }
    }
    else if(signal_type == SIGNAL_TYPE_SELL) {
        // Validate SL and TP for SELL
        if(sl > 0 && sl <= bid) sl = bid + g_atr_value;
        if(tp > 0 && tp >= bid) tp = bid - g_atr_value * 2;

        result = trade.Sell(lot_size, _Symbol, bid, sl, tp, comment);
        if(result) {
            Print("SELL order executed: Lot=", lot_size, " Price=", bid, " SL=", sl, " TP=", tp, " Confidence=", confidence);
        }
    }

    if(!result) {
        Print("Trade execution failed: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage                     |
//+------------------------------------------------------------------+
double CalculateLotSize() {
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double risk_amount = equity * (RiskPercent / 100.0);
    double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    if(tick_value == 0 || tick_size == 0 || g_atr_value == 0) {
        return DefaultLotSize;
    }

    double stop_distance = g_atr_value;
    double lot_size = risk_amount / (stop_distance / tick_size * tick_value);

    // Normalize lot size
    double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
    lot_size = MathFloor(lot_size / lot_step) * lot_step;

    return lot_size;
}

//+------------------------------------------------------------------+
//| ORDER BLOCK STRATEGY IMPLEMENTATION                             |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Run Order Block strategy                                         |
//+------------------------------------------------------------------+
void RunOrderBlockStrategy() {
    // Detect order blocks on higher timeframes
    if(OB_ShowH1Blocks && _Period < PERIOD_H1) {
        DetectOrderBlocks(PERIOD_H1);
    }
    if(OB_ShowH4Blocks && _Period < PERIOD_H4) {
        DetectOrderBlocks(PERIOD_H4);
    }
    if(OB_ShowD1Blocks && _Period < PERIOD_D1) {
        DetectOrderBlocks(PERIOD_D1);
    }

    // Update existing blocks
    UpdateOrderBlocks();

    // Generate signals from valid blocks
    TradingSignal signal = GenerateOrderBlockSignal();
    if(signal.is_valid) {
        UpdateStrategySignal(STRATEGY_ORDER_BLOCK, signal);
    }

    // Clean up old/invalid blocks
    CleanupOrderBlocks();
}

//+------------------------------------------------------------------+
//| Detect order blocks on specified timeframe                      |
//+------------------------------------------------------------------+
void DetectOrderBlocks(ENUM_TIMEFRAMES tf) {
    int bars_count = MathMin(500, iBars(_Symbol, tf));
    if(bars_count < OB_SwingLength * 2) return;

    // Get price data
    double high[], low[], open[], close[];
    long volume[];
    datetime time[];

    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(open, true);
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(volume, true);
    ArraySetAsSeries(time, true);

    if(CopyHigh(_Symbol, tf, 0, bars_count, high) <= 0) return;
    if(CopyLow(_Symbol, tf, 0, bars_count, low) <= 0) return;
    if(CopyOpen(_Symbol, tf, 0, bars_count, open) <= 0) return;
    if(CopyClose(_Symbol, tf, 0, bars_count, close) <= 0) return;
    if(CopyTickVolume(_Symbol, tf, 0, bars_count, volume) <= 0) return;
    if(CopyTime(_Symbol, tf, 0, bars_count, time) <= 0) return;

    // Scan for swing points
    for(int i = OB_SwingLength; i < bars_count - OB_SwingLength - 1; i++) {
        // Check for swing high
        if(IsSwingHigh(high, i, OB_SwingLength)) {
            // Look for bullish order block
            int ob_index = FindOrderBlockCandle(open, close, high, low, volume, i, true, tf);
            if(ob_index > 0) {
                CreateOrderBlock(time[ob_index], high[ob_index], low[ob_index],
                               tf, true, open[ob_index], close[ob_index]);
            }
        }

        // Check for swing low
        if(IsSwingLow(low, i, OB_SwingLength)) {
            // Look for bearish order block
            int ob_index = FindOrderBlockCandle(open, close, high, low, volume, i, false, tf);
            if(ob_index > 0) {
                CreateOrderBlock(time[ob_index], high[ob_index], low[ob_index],
                               tf, false, open[ob_index], close[ob_index]);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check if index is a swing high                                  |
//+------------------------------------------------------------------+
bool IsSwingHigh(const double &high[], int index, int swing_length) {
    double current_high = high[index];

    // Check left side
    for(int i = 1; i <= swing_length; i++) {
        if(high[index + i] >= current_high) return false;
    }

    // Check right side
    for(int i = 1; i <= swing_length; i++) {
        if(high[index - i] >= current_high) return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check if index is a swing low                                   |
//+------------------------------------------------------------------+
bool IsSwingLow(const double &low[], int index, int swing_length) {
    double current_low = low[index];

    // Check left side
    for(int i = 1; i <= swing_length; i++) {
        if(low[index + i] <= current_low) return false;
    }

    // Check right side
    for(int i = 1; i <= swing_length; i++) {
        if(low[index - i] <= current_low) return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Find order block candle                                         |
//+------------------------------------------------------------------+
int FindOrderBlockCandle(const double &open[], const double &close[], const double &high[],
                        const double &low[], const long &volume[], int swing_index,
                        bool is_bullish, ENUM_TIMEFRAMES tf) {
    int search_range = 10;
    int best_index = -1;
    long best_volume = 0;

    for(int i = swing_index + 1; i <= swing_index + search_range; i++) {
        if(i >= ArraySize(open)) break;

        bool is_valid_block = false;

        if(is_bullish) {
            // For bullish blocks, look for bearish candles before the swing high
            is_valid_block = (close[i] < open[i]) && (volume[i] > best_volume);
        } else {
            // For bearish blocks, look for bullish candles before the swing low
            is_valid_block = (close[i] > open[i]) && (volume[i] > best_volume);
        }

        if(is_valid_block) {
            best_index = i;
            best_volume = volume[i];
        }
    }

    return best_index;
}

//+------------------------------------------------------------------+
//| Create order block                                              |
//+------------------------------------------------------------------+
void CreateOrderBlock(datetime time, double high, double low, ENUM_TIMEFRAMES tf,
                     bool is_bullish, double open_price, double close_price) {
    // Check if block already exists
    for(int i = 0; i < g_block_count; i++) {
        if(MathAbs(g_order_blocks[i].high_price - high) < _Point &&
           MathAbs(g_order_blocks[i].low_price - low) < _Point &&
           g_order_blocks[i].timeframe == tf) {
            return; // Block already exists
        }
    }

    // Resize array if needed
    if(g_block_count >= ArraySize(g_order_blocks)) {
        ArrayResize(g_order_blocks, g_block_count + 50);
    }

    // Create new block
    OrderBlock new_block;
    new_block.time_created = time;
    new_block.high_price = high;
    new_block.low_price = low;
    new_block.open_price = open_price;
    new_block.close_price = close_price;
    new_block.timeframe = tf;
    new_block.is_bullish = is_bullish;
    new_block.is_fresh = true;
    new_block.is_broken = false;
    new_block.touches = 0;
    new_block.last_touch = 0;
    new_block.strength = CalculateBlockStrength(high, low, open_price, close_price, tf);
    new_block.obj_name = "OB_" + IntegerToString(time) + "_" + EnumToString(tf);
    new_block.signal_sent = false;
    new_block.partial_fill_ratio = 0.0;

    g_order_blocks[g_block_count] = new_block;
    g_block_count++;

    // Create visual representation
    CreateBlockVisual(g_block_count - 1);
}

//+------------------------------------------------------------------+
//| Calculate block strength                                         |
//+------------------------------------------------------------------+
double CalculateBlockStrength(double high, double low, double open_price,
                            double close_price, ENUM_TIMEFRAMES tf) {
    double strength = 1.0;

    // Base strength on candle size relative to ATR
    double candle_size = high - low;
    if(g_atr_value > 0) {
        strength += (candle_size / g_atr_value) * 0.5;
    }

    // Add strength based on timeframe
    switch(tf) {
        case PERIOD_D1: strength += 3.0; break;
        case PERIOD_H4: strength += 2.0; break;
        case PERIOD_H1: strength += 1.0; break;
        default: strength += 0.5; break;
    }

    // Add strength based on candle type (engulfing patterns get higher strength)
    double body_size = MathAbs(close_price - open_price);
    if(body_size > candle_size * 0.7) {
        strength += 0.5;
    }

    return strength;
}

//+------------------------------------------------------------------+
//| Create visual representation of order block                     |
//+------------------------------------------------------------------+
void CreateBlockVisual(int block_index) {
    if(block_index < 0 || block_index >= g_block_count) return;

    OrderBlock block = g_order_blocks[block_index];
    color block_color = block.is_bullish ? clrBlue : clrRed;

    // Create rectangle
    ObjectCreate(0, block.obj_name, OBJ_RECTANGLE, 0, block.time_created, block.high_price,
                TimeCurrent() + PeriodSeconds() * 100, block.low_price);
    ObjectSetInteger(0, block.obj_name, OBJPROP_COLOR, block_color);
    ObjectSetInteger(0, block.obj_name, OBJPROP_STYLE, STYLE_SOLID);
    ObjectSetInteger(0, block.obj_name, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, block.obj_name, OBJPROP_FILL, true);
    ObjectSetInteger(0, block.obj_name, OBJPROP_BACK, true);
    ObjectSetInteger(0, block.obj_name, OBJPROP_SELECTABLE, false);

    // Create label
    string label_text = (block.is_bullish ? "Bull OB " : "Bear OB ") +
                       EnumToString(block.timeframe) + " (" +
                       DoubleToString(block.strength, 1) + ")";
    ObjectCreate(0, block.obj_name + "_label", OBJ_TEXT, 0, block.time_created,
                block.is_bullish ? block.low_price : block.high_price);
    ObjectSetString(0, block.obj_name + "_label", OBJPROP_TEXT, label_text);
    ObjectSetInteger(0, block.obj_name + "_label", OBJPROP_COLOR, block_color);
    ObjectSetInteger(0, block.obj_name + "_label", OBJPROP_FONTSIZE, 8);
    ObjectSetInteger(0, block.obj_name + "_label", OBJPROP_SELECTABLE, false);
}

//+------------------------------------------------------------------+
//| Update order blocks                                              |
//+------------------------------------------------------------------+
void UpdateOrderBlocks() {
    double current_high = iHigh(_Symbol, _Period, 0);
    double current_low = iLow(_Symbol, _Period, 0);
    double current_close = iClose(_Symbol, _Period, 0);

    for(int i = 0; i < g_block_count; i++) {
        if(g_order_blocks[i].is_broken) continue;

        // Check if price is touching the block
        bool is_touching = false;
        if(g_order_blocks[i].is_bullish) {
            is_touching = (current_low <= g_order_blocks[i].high_price &&
                          current_low >= g_order_blocks[i].low_price);
        } else {
            is_touching = (current_high >= g_order_blocks[i].low_price &&
                          current_high <= g_order_blocks[i].high_price);
        }

        if(is_touching) {
            g_order_blocks[i].touches++;
            g_order_blocks[i].last_touch = TimeCurrent();

            // Check for block break
            CheckBlockBreak(i, current_high, current_low);
        }

        // Update visual representation
        UpdateBlockVisual(i);
    }
}

//+------------------------------------------------------------------+
//| Check if block is broken                                         |
//+------------------------------------------------------------------+
void CheckBlockBreak(int block_index, double current_high, double current_low) {
    if(block_index < 0 || block_index >= g_block_count) return;

    if(g_order_blocks[block_index].is_broken) return;

    bool is_broken = false;

    if(g_order_blocks[block_index].is_bullish) {
        // Bullish block is broken if price closes below the low
        is_broken = (current_low < g_order_blocks[block_index].low_price);
    } else {
        // Bearish block is broken if price closes above the high
        is_broken = (current_high > g_order_blocks[block_index].high_price);
    }

    if(is_broken) {
        g_order_blocks[block_index].is_broken = true;
        g_order_blocks[block_index].is_fresh = false;

        // Update visual to show broken state
        ObjectSetInteger(0, g_order_blocks[block_index].obj_name, OBJPROP_STYLE, STYLE_DOT);
        ObjectSetInteger(0, g_order_blocks[block_index].obj_name, OBJPROP_COLOR, clrGray);
    }
}

//+------------------------------------------------------------------+
//| Update block visual representation                               |
//+------------------------------------------------------------------+
void UpdateBlockVisual(int block_index) {
    if(block_index < 0 || block_index >= g_block_count) return;

    OrderBlock block = g_order_blocks[block_index];

    // Update rectangle end time
    ObjectSetInteger(0, block.obj_name, OBJPROP_TIME, 1, TimeCurrent() + PeriodSeconds() * 100);

    // Update transparency based on freshness
    int transparency = block.is_fresh ? 50 : 80;
    if(block.is_broken) transparency = 90;

    // Note: MQL5 doesn't have direct transparency control for rectangles
    // This is a placeholder for visual enhancement
}

//+------------------------------------------------------------------+
//| Generate Order Block signal                                      |
//+------------------------------------------------------------------+
TradingSignal GenerateOrderBlockSignal() {
    TradingSignal signal;
    signal.signal_type = SIGNAL_TYPE_HOLD;
    signal.confidence_level = 0.0;
    signal.stop_loss = 0.0;
    signal.take_profit = 0.0;
    signal.parameters = "";
    signal.strategy_name = "Order Block";
    signal.timestamp = TimeCurrent();
    signal.is_valid = false;

    // Find the best fresh, unbroken order block
    int best_block = -1;
    double best_strength = 0.0;

    for(int i = 0; i < g_block_count; i++) {
        OrderBlock block = g_order_blocks[i];
        if(block.is_fresh && !block.is_broken && !block.signal_sent &&
           block.strength > best_strength && block.strength >= OB_MinBlockStrength) {
            best_block = i;
            best_strength = block.strength;
        }
    }

    if(best_block >= 0) {
        OrderBlock block = g_order_blocks[best_block];
        double current_price = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) + SymbolInfoDouble(_Symbol, SYMBOL_BID)) / 2;

        // Check if price is near the block
        double distance_to_block = 0;
        if(block.is_bullish) {
            distance_to_block = MathAbs(current_price - block.low_price);
        } else {
            distance_to_block = MathAbs(current_price - block.high_price);
        }

        // Generate signal if price is close enough
        if(distance_to_block <= g_atr_value * 0.5) {
            if(block.is_bullish) {
                signal.signal_type = SIGNAL_TYPE_BUY;
                signal.stop_loss = block.low_price - g_atr_value * 0.5;
                signal.take_profit = block.high_price + (block.high_price - block.low_price) * 2;
            } else {
                signal.signal_type = SIGNAL_TYPE_SELL;
                signal.stop_loss = block.high_price + g_atr_value * 0.5;
                signal.take_profit = block.low_price - (block.high_price - block.low_price) * 2;
            }

            signal.confidence_level = MathMin(0.9, block.strength / 5.0);
            signal.parameters = "Block_" + IntegerToString(best_block) + "_" + EnumToString(block.timeframe);
            signal.is_valid = true;

            // Mark signal as sent
            g_order_blocks[best_block].signal_sent = true;
        }
    }

    return signal;
}

//+------------------------------------------------------------------+
//| Clean up old order blocks                                        |
//+------------------------------------------------------------------+
void CleanupOrderBlocks() {
    datetime current_time = TimeCurrent();

    for(int i = g_block_count - 1; i >= 0; i--) {
        bool should_remove = false;

        // Remove blocks older than 24 hours
        if(current_time - g_order_blocks[i].time_created > 86400) {
            should_remove = true;
        }
        // Remove broken blocks after 1 hour
        else if(g_order_blocks[i].is_broken &&
                current_time - g_order_blocks[i].last_touch > 3600) {
            should_remove = true;
        }
        // Remove blocks with too many touches (likely invalid)
        else if(g_order_blocks[i].touches > 5) {
            should_remove = true;
        }

        if(should_remove) {
            // Remove visual objects
            ObjectDelete(0, g_order_blocks[i].obj_name);
            ObjectDelete(0, g_order_blocks[i].obj_name + "_label");

            // Remove from array
            for(int j = i; j < g_block_count - 1; j++) {
                g_order_blocks[j] = g_order_blocks[j + 1];
            }
            g_block_count--;
            ArrayResize(g_order_blocks, g_block_count);
        }
    }
}

//+------------------------------------------------------------------+
//| FAIR VALUE GAP STRATEGY IMPLEMENTATION                          |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Run Fair Value Gap strategy                                      |
//+------------------------------------------------------------------+
void RunFairValueGapStrategy() {
    TradingSignal signal = GenerateFairValueGapSignal();
    if(signal.is_valid) {
        UpdateStrategySignal(STRATEGY_FAIR_VALUE_GAP, signal);
    }
}

//+------------------------------------------------------------------+
//| Generate Fair Value Gap signal                                   |
//+------------------------------------------------------------------+
TradingSignal GenerateFairValueGapSignal() {
    TradingSignal signal;
    signal.signal_type = SIGNAL_TYPE_HOLD;
    signal.confidence_level = 0.0;
    signal.stop_loss = 0.0;
    signal.take_profit = 0.0;
    signal.parameters = "";
    signal.strategy_name = "Fair Value Gap";
    signal.timestamp = TimeCurrent();
    signal.is_valid = false;

    // Check last 10 bars for FVG patterns
    for(int i = 1; i <= 10; i++) {
        double high = iHigh(_Symbol, _Period, i);
        double low = iLow(_Symbol, _Period, i);
        double open = iOpen(_Symbol, _Period, i);
        double close = iClose(_Symbol, _Period, i);

        if(i > 0) {
            double prevHigh = iHigh(_Symbol, _Period, i+1);
            double prevLow = iLow(_Symbol, _Period, i+1);

            // Bullish FVG: gap between current low and previous high
            if(low > prevHigh) {
                double gap_size = (low - prevHigh) / _Point;
                if(gap_size >= FVG_MinGapSize) {
                    signal.signal_type = SIGNAL_TYPE_BUY;
                    signal.confidence_level = 0.7;
                    signal.stop_loss = low - (high - low) * 0.5;
                    signal.take_profit = high + (high - low) * 2;
                    signal.parameters = "FVG_Bullish_" + IntegerToString(i);
                    signal.is_valid = true;
                    break;
                }
            }
            // Bearish FVG: gap between current high and previous low
            else if(high < prevLow) {
                double gap_size = (prevLow - high) / _Point;
                if(gap_size >= FVG_MinGapSize) {
                    signal.signal_type = SIGNAL_TYPE_SELL;
                    signal.confidence_level = 0.7;
                    signal.stop_loss = high + (high - low) * 0.5;
                    signal.take_profit = low - (high - low) * 2;
                    signal.parameters = "FVG_Bearish_" + IntegerToString(i);
                    signal.is_valid = true;
                    break;
                }
            }
        }

        // Check for Morning Star pattern
        if(i >= 2) {
            if(CheckMorningStarPattern(i)) {
                signal.signal_type = SIGNAL_TYPE_BUY;
                signal.confidence_level = 0.6;
                signal.stop_loss = iLow(_Symbol, _Period, i) - g_atr_value;
                signal.take_profit = iHigh(_Symbol, _Period, i) + g_atr_value * 2;
                signal.parameters = "MorningStar_" + IntegerToString(i);
                signal.is_valid = true;
                break;
            }
        }

        // Check for Hammer pattern
        if(CheckHammerPattern(i)) {
            signal.signal_type = SIGNAL_TYPE_BUY;
            signal.confidence_level = 0.65;
            signal.stop_loss = iLow(_Symbol, _Period, i) - g_atr_value * 0.5;
            signal.take_profit = iHigh(_Symbol, _Period, i) + g_atr_value * 1.5;
            signal.parameters = "Hammer_" + IntegerToString(i);
            signal.is_valid = true;
            break;
        }
    }

    return signal;
}

//+------------------------------------------------------------------+
//| Check for Morning Star pattern                                   |
//+------------------------------------------------------------------+
bool CheckMorningStarPattern(int index) {
    if(index < 2) return false;

    double open1 = iOpen(_Symbol, _Period, index);
    double close1 = iClose(_Symbol, _Period, index);
    double high1 = iHigh(_Symbol, _Period, index);
    double low1 = iLow(_Symbol, _Period, index);

    double open2 = iOpen(_Symbol, _Period, index + 1);
    double close2 = iClose(_Symbol, _Period, index + 1);
    double high2 = iHigh(_Symbol, _Period, index + 1);
    double low2 = iLow(_Symbol, _Period, index + 1);

    double open3 = iOpen(_Symbol, _Period, index + 2);
    double close3 = iClose(_Symbol, _Period, index + 2);

    double size1 = MathAbs(close1 - open1);
    double size2 = MathAbs(close2 - open2);
    double size3 = MathAbs(close3 - open3);

    // Morning Star: bearish -> small -> bullish
    if(open1 < close1 && open3 > close3) {
        if(size2 < size1 * FVG_MaxMiddleCandleRatio && size2 < size3 * FVG_MaxMiddleCandleRatio) {
            return true;
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//| Check for Hammer pattern                                         |
//+------------------------------------------------------------------+
bool CheckHammerPattern(int index) {
    double high = iHigh(_Symbol, _Period, index);
    double low = iLow(_Symbol, _Period, index);
    double open = iOpen(_Symbol, _Period, index);
    double close = iClose(_Symbol, _Period, index);

    double candleSize = high - low;
    if(candleSize == 0) return false;

    double maxRatioShortShadow = 0.1;  // 10% max for short shadow
    double minRatioLongShadow = 0.6;   // 60% min for long shadow

    // Green hammer
    if(open < close) {
        if(high - close < candleSize * maxRatioShortShadow) {
            if(open - low > candleSize * minRatioLongShadow) {
                return true;
            }
        }
    }
    // Red hammer
    else if(open > close) {
        if(high - open < candleSize * maxRatioShortShadow) {
            if(close - low > candleSize * minRatioLongShadow) {
                return true;
            }
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//| MARKET STRUCTURE STRATEGY IMPLEMENTATION                        |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Run Market Structure strategy                                    |
//+------------------------------------------------------------------+
void RunMarketStructureStrategy() {
    TradingSignal signal = GenerateMarketStructureSignal();
    if(signal.is_valid) {
        UpdateStrategySignal(STRATEGY_MARKET_STRUCTURE, signal);
    }
}

//+------------------------------------------------------------------+
//| Generate Market Structure signal                                 |
//+------------------------------------------------------------------+
TradingSignal GenerateMarketStructureSignal() {
    TradingSignal signal;
    signal.signal_type = SIGNAL_TYPE_HOLD;
    signal.confidence_level = 0.0;
    signal.stop_loss = 0.0;
    signal.take_profit = 0.0;
    signal.parameters = "";
    signal.strategy_name = "Market Structure";
    signal.timestamp = TimeCurrent();
    signal.is_valid = false;

    // Simple swing high/low detection
    static double lastSwingHigh = -1.0;
    static double lastSwingLow = -1.0;

    double high = iHigh(_Symbol, _Period, 1);
    double low = iLow(_Symbol, _Period, 1);
    double prevHigh = iHigh(_Symbol, _Period, 2);
    double prevLow = iLow(_Symbol, _Period, 2);

    // Detect swing high break (bearish structure break)
    if(high > lastSwingHigh && high > prevHigh) {
        lastSwingHigh = high;
        signal.signal_type = SIGNAL_TYPE_SELL;
        signal.confidence_level = 0.6;
        signal.stop_loss = high + g_atr_value * 0.5;
        signal.take_profit = low - g_atr_value * 2;
        signal.parameters = "SwingHigh_Break_" + DoubleToString(high, _Digits);
        signal.is_valid = true;
    }
    // Detect swing low break (bullish structure break)
    else if(low < lastSwingLow && low < prevLow) {
        lastSwingLow = low;
        signal.signal_type = SIGNAL_TYPE_BUY;
        signal.confidence_level = 0.6;
        signal.stop_loss = low - g_atr_value * 0.5;
        signal.take_profit = high + g_atr_value * 2;
        signal.parameters = "SwingLow_Break_" + DoubleToString(low, _Digits);
        signal.is_valid = true;
    }

    return signal;
}

//+------------------------------------------------------------------+
//| RANGE BREAKOUT STRATEGY IMPLEMENTATION                          |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Run Range Breakout strategy                                      |
//+------------------------------------------------------------------+
void RunRangeBreakoutStrategy() {
    // Update daily range
    UpdateDailyRange();

    TradingSignal signal = GenerateRangeBreakoutSignal();
    if(signal.is_valid) {
        UpdateStrategySignal(STRATEGY_RANGE_BREAKOUT, signal);
    }
}

//+------------------------------------------------------------------+
//| Update daily range                                               |
//+------------------------------------------------------------------+
void UpdateDailyRange() {
    datetime current_time = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(current_time, dt);

    // Reset range at start of new day
    static int last_day = -1;
    if(dt.day != last_day) {
        g_daily_high = 0;
        g_daily_low = 0;
        g_range_established = false;
        g_range_broken = false;
        last_day = dt.day;
    }

    // Calculate range during specified hours
    if(dt.hour >= 0 && dt.hour < RB_ValidBreakStartHour) {
        double current_high = iHigh(_Symbol, _Period, 0);
        double current_low = iLow(_Symbol, _Period, 0);

        if(g_daily_high == 0 || current_high > g_daily_high) {
            g_daily_high = current_high;
        }
        if(g_daily_low == 0 || current_low < g_daily_low) {
            g_daily_low = current_low;
        }

        g_range_established = true;
    }
}

//+------------------------------------------------------------------+
//| Generate Range Breakout signal                                   |
//+------------------------------------------------------------------+
TradingSignal GenerateRangeBreakoutSignal() {
    TradingSignal signal;
    signal.signal_type = SIGNAL_TYPE_HOLD;
    signal.confidence_level = 0.0;
    signal.stop_loss = 0.0;
    signal.take_profit = 0.0;
    signal.parameters = "";
    signal.strategy_name = "Range Breakout";
    signal.timestamp = TimeCurrent();
    signal.is_valid = false;

    if(!g_range_established || g_range_broken) return signal;

    MqlDateTime dt;
    TimeToStruct(TimeCurrent(), dt);

    // Check for breakout during valid hours
    if(dt.hour >= RB_ValidBreakStartHour && dt.hour <= RB_ValidBreakEndHour) {
        double current_close = iClose(_Symbol, _Period, 1);

        // Bullish breakout
        if(current_close > g_daily_high) {
            signal.signal_type = SIGNAL_TYPE_BUY;
            signal.confidence_level = 0.8;
            signal.stop_loss = g_daily_low;
            signal.take_profit = current_close + (g_daily_high - g_daily_low) * 2;
            signal.parameters = "RangeBreakout_Bullish_" + DoubleToString(g_daily_high, _Digits);
            signal.is_valid = true;
            g_range_broken = true;
        }
        // Bearish breakout
        else if(current_close < g_daily_low) {
            signal.signal_type = SIGNAL_TYPE_SELL;
            signal.confidence_level = 0.8;
            signal.stop_loss = g_daily_high;
            signal.take_profit = current_close - (g_daily_high - g_daily_low) * 2;
            signal.parameters = "RangeBreakout_Bearish_" + DoubleToString(g_daily_low, _Digits);
            signal.is_valid = true;
            g_range_broken = true;
        }
    }

    return signal;
}

//+------------------------------------------------------------------+
//| SUPPORT/RESISTANCE STRATEGY IMPLEMENTATION                      |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Run Support/Resistance strategy                                  |
//+------------------------------------------------------------------+
void RunSupportResistanceStrategy() {
    // Update support/resistance levels
    UpdateSupportResistanceLevels();

    TradingSignal signal = GenerateSupportResistanceSignal();
    if(signal.is_valid) {
        UpdateStrategySignal(STRATEGY_SUPPORT_RESISTANCE, signal);
    }
}

//+------------------------------------------------------------------+
//| Update support and resistance levels                             |
//+------------------------------------------------------------------+
void UpdateSupportResistanceLevels() {
    int lookback = MathMin(SR_LookbackPeriod, iBars(_Symbol, _Period));
    if(lookback < 20) return;

    // Find potential support and resistance levels
    for(int i = 10; i < lookback - 10; i++) {
        double high = iHigh(_Symbol, _Period, i);
        double low = iLow(_Symbol, _Period, i);
        datetime time = iTime(_Symbol, _Period, i);

        // Check for resistance level
        for(int j = i + 10; j < lookback; j++) {
            double high_j = iHigh(_Symbol, _Period, j);
            datetime time_j = iTime(_Symbol, _Period, j);

            double high_diff = MathAbs(high - high_j) / _Point;
            if(high_diff <= SR_LevelTolerance) {
                // Found matching resistance level
                if(g_resistance_levels[0] != high && g_resistance_levels[1] != high_j) {
                    g_resistance_levels[0] = high;
                    g_resistance_levels[1] = high_j;

                    // Draw resistance level
                    string res_name = "RESISTANCE_LEVEL";
                    ObjectDelete(0, res_name);
                    ObjectCreate(0, res_name, OBJ_HLINE, 0, 0, high);
                    ObjectSetInteger(0, res_name, OBJPROP_COLOR, clrRed);
                    ObjectSetInteger(0, res_name, OBJPROP_WIDTH, 2);
                    ObjectSetInteger(0, res_name, OBJPROP_STYLE, STYLE_SOLID);
                }
                break;
            }
        }

        // Check for support level
        for(int j = i + 10; j < lookback; j++) {
            double low_j = iLow(_Symbol, _Period, j);
            datetime time_j = iTime(_Symbol, _Period, j);

            double low_diff = MathAbs(low - low_j) / _Point;
            if(low_diff <= SR_LevelTolerance) {
                // Found matching support level
                if(g_support_levels[0] != low && g_support_levels[1] != low_j) {
                    g_support_levels[0] = low;
                    g_support_levels[1] = low_j;

                    // Draw support level
                    string sup_name = "SUPPORT_LEVEL";
                    ObjectDelete(0, sup_name);
                    ObjectCreate(0, sup_name, OBJ_HLINE, 0, 0, low);
                    ObjectSetInteger(0, sup_name, OBJPROP_COLOR, clrBlue);
                    ObjectSetInteger(0, sup_name, OBJPROP_WIDTH, 2);
                    ObjectSetInteger(0, sup_name, OBJPROP_STYLE, STYLE_SOLID);
                }
                break;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Generate Support/Resistance signal                               |
//+------------------------------------------------------------------+
TradingSignal GenerateSupportResistanceSignal() {
    TradingSignal signal;
    signal.signal_type = SIGNAL_TYPE_HOLD;
    signal.confidence_level = 0.0;
    signal.stop_loss = 0.0;
    signal.take_profit = 0.0;
    signal.parameters = "";
    signal.strategy_name = "Support/Resistance";
    signal.timestamp = TimeCurrent();
    signal.is_valid = false;

    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    double open1 = iOpen(_Symbol, _Period, 1);
    double high1 = iHigh(_Symbol, _Period, 1);
    double low1 = iLow(_Symbol, _Period, 1);
    double close1 = iClose(_Symbol, _Period, 1);

    // Check for resistance level interaction
    if(g_resistance_levels[0] > 0) {
        double resistance_level = g_resistance_levels[0];

        // Bearish signal: price rejected at resistance
        if(open1 > close1 && open1 < resistance_level &&
           high1 > resistance_level && bid < resistance_level) {
            signal.signal_type = SIGNAL_TYPE_SELL;
            signal.confidence_level = 0.6;
            signal.stop_loss = resistance_level + g_atr_value * 0.5;
            signal.take_profit = bid - g_atr_value * 2;
            signal.parameters = "Resistance_Rejection_" + DoubleToString(resistance_level, _Digits);
            signal.is_valid = true;
        }
    }

    // Check for support level interaction
    if(g_support_levels[0] > 0 && !signal.is_valid) {
        double support_level = g_support_levels[0];

        // Bullish signal: price bounced from support
        if(open1 < close1 && open1 > support_level &&
           low1 < support_level && ask > support_level) {
            signal.signal_type = SIGNAL_TYPE_BUY;
            signal.confidence_level = 0.6;
            signal.stop_loss = support_level - g_atr_value * 0.5;
            signal.take_profit = ask + g_atr_value * 2;
            signal.parameters = "Support_Bounce_" + DoubleToString(support_level, _Digits);
            signal.is_valid = true;
        }
    }

    return signal;
}

//+------------------------------------------------------------------+
//| DASHBOARD IMPLEMENTATION                                         |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Create Dashboard                                                 |
//+------------------------------------------------------------------+
void CreateDashboard() {
    ObjectsDeleteAll(0, DASHBOARD_PREFIX); // Clear existing dashboard objects

    //--- Main Dashboard Background
    ObjectCreate(0, DASHBOARD_PREFIX + "MainBg", OBJ_RECTANGLE_LABEL, 0, 0, 0);
    ObjectSetInteger(0, DASHBOARD_PREFIX + "MainBg", OBJPROP_XSIZE, 3000);
    ObjectSetInteger(0, DASHBOARD_PREFIX + "MainBg", OBJPROP_YSIZE, 2000);
    ObjectSetInteger(0, DASHBOARD_PREFIX + "MainBg", OBJPROP_COLOR, COLOR_BACKGROUND);
    ObjectSetInteger(0, DASHBOARD_PREFIX + "MainBg", OBJPROP_BACK, true);
    ObjectSetInteger(0, DASHBOARD_PREFIX + "MainBg", OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, DASHBOARD_PREFIX + "MainBg", OBJPROP_CORNER, CORNER_LEFT_UPPER);

    //--- Dashboard Title
    CreateLabel(DASHBOARD_PREFIX + "Title", START_X, START_Y - 35, "CONSOLIDATED MISAPE BOT - MULTI-STRATEGY DASHBOARD", "Arial Black", 16, COLOR_TEXT_ACCENT);

    //--- Main Status Panel
    int main_panel_x = START_X + (CARD_WIDTH + SPACING_X) * 2 + SPACING_X;
    int main_panel_y = START_Y;
    int main_panel_w = (int)(CARD_WIDTH * 1.5);
    int main_panel_h = (int)(CARD_HEIGHT * 2 + SPACING_Y);

    CreatePanel(DASHBOARD_PREFIX + "MainStatusPanel", main_panel_x, main_panel_y, main_panel_w, main_panel_h, "Master Status");
    CreateLabel(DASHBOARD_PREFIX + "StatusLabel", main_panel_x + 15, main_panel_y + 40, "Bot Status:", "Arial", 10, COLOR_TEXT_NORMAL);
    CreateLabel(DASHBOARD_PREFIX + "StatusValue", main_panel_x + 100, main_panel_y + 40, "OPERATIONAL", "Arial", 10, COLOR_BUY);
    CreateLabel(DASHBOARD_PREFIX + "TradesLabel", main_panel_x + 15, main_panel_y + 65, "Active Trades:", "Arial", 10, COLOR_TEXT_NORMAL);
    CreateLabel(DASHBOARD_PREFIX + "TradesValue", main_panel_x + 100, main_panel_y + 65, "0", "Arial", 10, COLOR_TEXT_HEADER);
    CreateLabel(DASHBOARD_PREFIX + "ProfitLabel", main_panel_x + 15, main_panel_y + 90, "Total P&L:", "Arial", 10, COLOR_TEXT_NORMAL);
    CreateLabel(DASHBOARD_PREFIX + "ProfitValue", main_panel_x + 100, main_panel_y + 90, "$0.00", "Arial", 10, COLOR_TEXT_HEADER);

    //--- Create Strategy Cards (2-column layout)
    for(int i = 0; i < 6; i++) {
        int col = i % 2;
        int row = i / 2;
        int x_pos = START_X + col * (CARD_WIDTH + SPACING_X);
        int y_pos = START_Y + row * (CARD_HEIGHT + SPACING_Y);
        string card_name = DASHBOARD_PREFIX + g_strategies[i].name;

        CreatePanel(card_name, x_pos, y_pos, CARD_WIDTH, CARD_HEIGHT, g_strategies[i].name);

        // Signal Info
        CreateLabel(card_name + "_SignalLabel", x_pos + 15, y_pos + 40, "Signal:", "Arial", 10, COLOR_TEXT_NORMAL);
        CreateLabel(card_name + "_SignalValue", x_pos + 90, y_pos + 40, "N/A", "Arial", 10, COLOR_HOLD);
        CreateLabel(card_name + "_ConfLabel", x_pos + 15, y_pos + 60, "Confidence:", "Arial", 10, COLOR_TEXT_NORMAL);
        CreateLabel(card_name + "_ConfValue", x_pos + 90, y_pos + 60, "0.00", "Arial", 10, COLOR_TEXT_NORMAL);
        CreateLabel(card_name + "_UpdateLabel", x_pos + 15, y_pos + 80, "Updated:", "Arial", 10, COLOR_TEXT_NORMAL);
        CreateLabel(card_name + "_UpdateValue", x_pos + 90, y_pos + 80, "-", "Arial", 8, COLOR_TEXT_NORMAL);
    }

    ChartRedraw();
}

//+------------------------------------------------------------------+
//| Helper function to create a text label                           |
//+------------------------------------------------------------------+
void CreateLabel(string name, int x, int y, string text, string font = "Arial", int size = 10, color clr = clrWhite) {
    ObjectCreate(0, name, OBJ_LABEL, 0, x, y);
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    ObjectSetString(0, name, OBJPROP_FONT, font);
    ObjectSetInteger(0, name, OBJPROP_FONTSIZE, size);
    ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    ObjectSetInteger(0, name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
    ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
    ObjectSetInteger(0, name, OBJPROP_BACK, false);
}

//+------------------------------------------------------------------+
//| Helper function to update a text label                           |
//+------------------------------------------------------------------+
void UpdateLabel(string name, string text, color clr = clrNONE) {
    if(ObjectFind(0, name) < 0) return;
    ObjectSetString(0, name, OBJPROP_TEXT, text);
    if(clr != clrNONE) {
        ObjectSetInteger(0, name, OBJPROP_COLOR, clr);
    }
}

//+------------------------------------------------------------------+
//| Helper function to create a styled panel                         |
//+------------------------------------------------------------------+
void CreatePanel(string name, int x, int y, int w, int h, string title) {
    //--- Panel Background
    ObjectCreate(0, name + "_Bg", OBJ_RECTANGLE_LABEL, 0, x, y);
    ObjectSetInteger(0, name + "_Bg", OBJPROP_XSIZE, w);
    ObjectSetInteger(0, name + "_Bg", OBJPROP_YSIZE, h);
    ObjectSetInteger(0, name + "_Bg", OBJPROP_COLOR, COLOR_CARD_BG);
    ObjectSetInteger(0, name + "_Bg", OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, name + "_Bg", OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name + "_Bg", OBJPROP_SELECTABLE, false);

    //--- Panel Border
    ObjectCreate(0, name + "_Border", OBJ_RECTANGLE_LABEL, 0, x, y);
    ObjectSetInteger(0, name + "_Border", OBJPROP_XSIZE, w);
    ObjectSetInteger(0, name + "_Border", OBJPROP_YSIZE, h);
    ObjectSetInteger(0, name + "_Border", OBJPROP_COLOR, COLOR_CARD_BORDER);
    ObjectSetInteger(0, name + "_Border", OBJPROP_FILL, false);
    ObjectSetInteger(0, name + "_Border", OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, name + "_Border", OBJPROP_SELECTABLE, false);

    //--- Panel Title
    CreateLabel(name + "_Title", x + 10, y + 10, title, "Arial", 12, COLOR_TEXT_HEADER);
}

//+------------------------------------------------------------------+
//| Update Dashboard                                                 |
//+------------------------------------------------------------------+
void UpdateDashboard() {
    //--- Update Strategy Cards
    for(int i = 0; i < 6; i++) {
        string card_name = DASHBOARD_PREFIX + g_strategies[i].name;
        string signal_val, conf_val, update_val;
        color signal_color, border_color;

        if(g_strategies[i].last_signal.is_valid) {
            TradingSignal signal = g_strategies[i].last_signal;
            signal_val = GetSignalTypeString(signal.signal_type);
            conf_val = DoubleToString(signal.confidence_level, 2);
            update_val = TimeToString(g_strategies[i].last_updated, TIME_SECONDS);

            switch(signal.signal_type) {
                case SIGNAL_TYPE_BUY:  signal_color = COLOR_BUY;  border_color = COLOR_BUY;  break;
                case SIGNAL_TYPE_SELL: signal_color = COLOR_SELL; border_color = COLOR_SELL; break;
                default:               signal_color = COLOR_HOLD; border_color = COLOR_CARD_BORDER; break;
            }
        } else {
            signal_val = "N/A";
            conf_val = "0.00";
            update_val = "-";
            signal_color = COLOR_TEXT_NORMAL;
            border_color = COLOR_CARD_BORDER;
        }

        UpdateLabel(card_name + "_SignalValue", signal_val, signal_color);
        UpdateLabel(card_name + "_ConfValue", conf_val);
        UpdateLabel(card_name + "_UpdateValue", update_val);
        ObjectSetInteger(0, card_name + "_Border", OBJPROP_COLOR, border_color);
    }

    //--- Update Main Status Panel
    int active_trades = PositionsTotal();
    double total_pl = 0;
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == MagicNumber) {
            total_pl += PositionGetDouble(POSITION_PROFIT);
        }
    }

    UpdateLabel(DASHBOARD_PREFIX + "TradesValue", (string)active_trades);
    UpdateLabel(DASHBOARD_PREFIX + "ProfitValue", "$" + DoubleToString(total_pl, 2), total_pl >= 0 ? COLOR_PROFIT : COLOR_LOSS);
    UpdateLabel(DASHBOARD_PREFIX + "StatusValue", EnableTrading ? "TRADING ACTIVE" : "TRADING DISABLED", EnableTrading ? COLOR_BUY : COLOR_SELL);

    ChartRedraw();
}

//+------------------------------------------------------------------+
//| CHART PATTERN STRATEGY IMPLEMENTATION                           |
//+------------------------------------------------------------------+

// Chart Pattern structures
struct PatternPoint {
    datetime time;
    double price;
    int bar_index;
};

struct ChartPattern {
    string pattern_name;
    PatternPoint points[5];  // Max 5 points for complex patterns
    int point_count;
    bool is_bullish;
    double confidence;
    datetime formation_time;
    bool is_valid;
    string obj_name;
};

// Global pattern variables
ChartPattern g_detected_patterns[50];
int g_pattern_count = 0;

//+------------------------------------------------------------------+
//| Run Chart Pattern strategy                                       |
//+------------------------------------------------------------------+
void RunChartPatternStrategy() {
    // Clear old patterns
    CleanupOldPatterns();

    // Detect new patterns
    DetectHeadAndShouldersPattern();
    DetectFlagPattern();
    DetectButterflyPattern();

    // Generate trading signal from best pattern
    TradingSignal signal = GenerateChartPatternSignal();
    if(signal.is_valid) {
        UpdateStrategySignal(STRATEGY_CHART_PATTERN, signal);
    }
}

//+------------------------------------------------------------------+
//| Check RSI confirmation for chart patterns                       |
//+------------------------------------------------------------------+
bool CheckRSIConfirmation(bool is_bullish_pattern) {
    double rsi_values[1];
    if(CopyBuffer(g_rsi_handle, 0, 0, 1, rsi_values) <= 0) {
        return true; // If RSI fails, don't block the signal
    }

    double current_rsi = rsi_values[0];

    if(is_bullish_pattern) {
        return current_rsi <= CP_RSI_Oversold; // RSI oversold for bullish patterns
    } else {
        return current_rsi >= CP_RSI_Overbought; // RSI overbought for bearish patterns
    }
}

//+------------------------------------------------------------------+
//| Find swing highs and lows                                       |
//+------------------------------------------------------------------+
bool IsSwingHigh(int bar_index, int swing_length) {
    double current_high = iHigh(_Symbol, PERIOD_CURRENT, bar_index);

    for(int i = 1; i <= swing_length; i++) {
        if(bar_index + i >= iBars(_Symbol, PERIOD_CURRENT) || bar_index - i < 0) continue;

        double left_high = iHigh(_Symbol, PERIOD_CURRENT, bar_index + i);
        double right_high = iHigh(_Symbol, PERIOD_CURRENT, bar_index - i);

        if(left_high >= current_high || right_high >= current_high) {
            return false;
        }
    }
    return true;
}

bool IsSwingLow(int bar_index, int swing_length) {
    double current_low = iLow(_Symbol, PERIOD_CURRENT, bar_index);

    for(int i = 1; i <= swing_length; i++) {
        if(bar_index + i >= iBars(_Symbol, PERIOD_CURRENT) || bar_index - i < 0) continue;

        double left_low = iLow(_Symbol, PERIOD_CURRENT, bar_index + i);
        double right_low = iLow(_Symbol, PERIOD_CURRENT, bar_index - i);

        if(left_low <= current_low || right_low <= current_low) {
            return false;
        }
    }
    return true;
}

//+------------------------------------------------------------------+
//| Detect Head and Shoulders pattern                               |
//+------------------------------------------------------------------+
void DetectHeadAndShouldersPattern() {
    int bars_count = MathMin(200, iBars(_Symbol, PERIOD_CURRENT));
    if(bars_count < CP_SwingLength * 6) return;

    // Find swing highs for Head and Shoulders
    for(int i = CP_SwingLength * 3; i < bars_count - CP_SwingLength * 3; i++) {
        if(!IsSwingHigh(i, CP_SwingLength)) continue;

        double head_price = iHigh(_Symbol, PERIOD_CURRENT, i);
        datetime head_time = iTime(_Symbol, PERIOD_CURRENT, i);

        // Look for left shoulder
        for(int left = i + CP_SwingLength * 2; left < i + CP_SwingLength * 6 && left < bars_count; left++) {
            if(!IsSwingHigh(left, CP_SwingLength)) continue;

            double left_shoulder_price = iHigh(_Symbol, PERIOD_CURRENT, left);
            if(left_shoulder_price >= head_price * 0.95) continue; // Left shoulder should be lower than head

            // Look for right shoulder
            for(int right = i - CP_SwingLength * 2; right > i - CP_SwingLength * 6 && right >= 0; right--) {
                if(!IsSwingHigh(right, CP_SwingLength)) continue;

                double right_shoulder_price = iHigh(_Symbol, PERIOD_CURRENT, right);
                if(right_shoulder_price >= head_price * 0.95) continue; // Right shoulder should be lower than head

                // Check if shoulders are roughly equal
                double shoulder_ratio = MathAbs(left_shoulder_price - right_shoulder_price) / left_shoulder_price;
                if(shoulder_ratio > 0.05) continue; // Shoulders should be within 5% of each other

                // Find neckline (lows between shoulders and head)
                double left_neckline = FindLowestBetween(left, i);
                double right_neckline = FindLowestBetween(i, right);
                double neckline_price = MathMax(left_neckline, right_neckline);

                // Validate pattern size
                double pattern_size = (head_price - neckline_price) / _Point;
                if(pattern_size < CP_MinPatternSize) continue;

                // Create Head and Shoulders pattern
                ChartPattern pattern;
                pattern.pattern_name = "Head and Shoulders";
                pattern.point_count = 5;
                pattern.points[0].time = iTime(_Symbol, PERIOD_CURRENT, left);
                pattern.points[0].price = left_shoulder_price;
                pattern.points[1].time = head_time;
                pattern.points[1].price = head_price;
                pattern.points[2].time = iTime(_Symbol, PERIOD_CURRENT, right);
                pattern.points[2].price = right_shoulder_price;
                pattern.points[3].price = neckline_price;
                pattern.points[4].price = neckline_price;
                pattern.is_bullish = false; // Head and Shoulders is bearish
                pattern.confidence = CalculatePatternConfidence(pattern_size, shoulder_ratio);
                pattern.formation_time = TimeCurrent();
                pattern.is_valid = CheckRSIConfirmation(false);
                pattern.obj_name = "H&S_" + IntegerToString(TimeCurrent());

                if(pattern.is_valid && AddPattern(pattern)) {
                    if(CP_ShowPatterns) DrawHeadAndShouldersPattern(g_pattern_count - 1);
                    return; // Found valid pattern, exit
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Helper functions for pattern detection                          |
//+------------------------------------------------------------------+
double FindLowestBetween(int start_bar, int end_bar) {
    if(start_bar > end_bar) {
        int temp = start_bar;
        start_bar = end_bar;
        end_bar = temp;
    }

    double lowest = iLow(_Symbol, PERIOD_CURRENT, start_bar);
    for(int i = start_bar + 1; i <= end_bar; i++) {
        double current_low = iLow(_Symbol, PERIOD_CURRENT, i);
        if(current_low < lowest) lowest = current_low;
    }
    return lowest;
}

double FindHighestBetween(int start_bar, int end_bar) {
    if(start_bar > end_bar) {
        int temp = start_bar;
        start_bar = end_bar;
        end_bar = temp;
    }

    double highest = iHigh(_Symbol, PERIOD_CURRENT, start_bar);
    for(int i = start_bar + 1; i <= end_bar; i++) {
        double current_high = iHigh(_Symbol, PERIOD_CURRENT, i);
        if(current_high > highest) highest = current_high;
    }
    return highest;
}

double CalculatePatternConfidence(double pattern_size, double symmetry_ratio) {
    double confidence = 0.5; // Base confidence

    // Increase confidence based on pattern size
    if(pattern_size > CP_MinPatternSize * 2) confidence += 0.2;
    if(pattern_size > CP_MinPatternSize * 3) confidence += 0.1;

    // Increase confidence based on symmetry (lower ratio = better symmetry)
    if(symmetry_ratio < 0.02) confidence += 0.2;
    else if(symmetry_ratio < 0.05) confidence += 0.1;

    return MathMin(0.9, confidence);
}

bool AddPattern(ChartPattern &pattern) {
    if(g_pattern_count >= ArraySize(g_detected_patterns)) {
        // Remove oldest pattern to make room
        for(int i = 0; i < g_pattern_count - 1; i++) {
            g_detected_patterns[i] = g_detected_patterns[i + 1];
        }
        g_pattern_count--;
    }

    g_detected_patterns[g_pattern_count] = pattern;
    g_pattern_count++;
    return true;
}

//+------------------------------------------------------------------+
//| Detect Flag pattern                                             |
//+------------------------------------------------------------------+
void DetectFlagPattern() {
    int bars_count = MathMin(100, iBars(_Symbol, PERIOD_CURRENT));
    if(bars_count < CP_SwingLength * 4) return;

    // Look for strong trend followed by consolidation
    for(int i = CP_SwingLength * 2; i < bars_count - CP_SwingLength * 2; i++) {
        // Check for strong uptrend (flagpole)
        double pole_start = iClose(_Symbol, PERIOD_CURRENT, i + CP_SwingLength * 2);
        double pole_end = iClose(_Symbol, PERIOD_CURRENT, i);
        double pole_size = (pole_end - pole_start) / _Point;

        if(MathAbs(pole_size) < CP_MinPatternSize) continue;

        bool is_bullish_flag = pole_size > 0;

        // Look for consolidation (flag) after the pole
        double flag_high = FindHighestBetween(i - CP_SwingLength, i);
        double flag_low = FindLowestBetween(i - CP_SwingLength, i);
        double flag_size = (flag_high - flag_low) / _Point;

        // Flag should be smaller than pole
        if(flag_size > MathAbs(pole_size) * 0.5) continue;

        // Check if flag is sloping against the trend (optional)
        double flag_start_price = iClose(_Symbol, PERIOD_CURRENT, i);
        double flag_end_price = iClose(_Symbol, PERIOD_CURRENT, i - CP_SwingLength);

        ChartPattern pattern;
        pattern.pattern_name = is_bullish_flag ? "Bull Flag" : "Bear Flag";
        pattern.point_count = 4;
        pattern.points[0].time = iTime(_Symbol, PERIOD_CURRENT, i + CP_SwingLength * 2);
        pattern.points[0].price = pole_start;
        pattern.points[1].time = iTime(_Symbol, PERIOD_CURRENT, i);
        pattern.points[1].price = pole_end;
        pattern.points[2].time = iTime(_Symbol, PERIOD_CURRENT, i - CP_SwingLength);
        pattern.points[2].price = flag_end_price;
        pattern.points[3].price = flag_high;
        pattern.is_bullish = is_bullish_flag;
        pattern.confidence = CalculatePatternConfidence(MathAbs(pole_size), flag_size / MathAbs(pole_size));
        pattern.formation_time = TimeCurrent();
        pattern.is_valid = CheckRSIConfirmation(is_bullish_flag);
        pattern.obj_name = "Flag_" + IntegerToString(TimeCurrent());

        if(pattern.is_valid && AddPattern(pattern)) {
            if(CP_ShowPatterns) DrawFlagPattern(g_pattern_count - 1);
            return;
        }
    }
}

//+------------------------------------------------------------------+
//| Detect Butterfly pattern (simplified)                          |
//+------------------------------------------------------------------+
void DetectButterflyPattern() {
    int bars_count = MathMin(150, iBars(_Symbol, PERIOD_CURRENT));
    if(bars_count < CP_SwingLength * 8) return;

    // Look for ABCD pattern with Fibonacci ratios
    for(int i = CP_SwingLength * 4; i < bars_count - CP_SwingLength * 4; i++) {
        // Find potential X, A, B, C, D points
        if(!IsSwingHigh(i, CP_SwingLength) && !IsSwingLow(i, CP_SwingLength)) continue;

        double point_C = IsSwingHigh(i, CP_SwingLength) ? iHigh(_Symbol, PERIOD_CURRENT, i) : iLow(_Symbol, PERIOD_CURRENT, i);
        bool is_bullish_butterfly = IsSwingLow(i, CP_SwingLength);

        // Simplified butterfly detection - look for retracement patterns
        double pattern_size = MathAbs(point_C - iClose(_Symbol, PERIOD_CURRENT, i + CP_SwingLength * 2)) / _Point;
        if(pattern_size < CP_MinPatternSize) continue;

        ChartPattern pattern;
        pattern.pattern_name = is_bullish_butterfly ? "Bull Butterfly" : "Bear Butterfly";
        pattern.point_count = 3;
        pattern.points[0].time = iTime(_Symbol, PERIOD_CURRENT, i + CP_SwingLength * 2);
        pattern.points[0].price = iClose(_Symbol, PERIOD_CURRENT, i + CP_SwingLength * 2);
        pattern.points[1].time = iTime(_Symbol, PERIOD_CURRENT, i);
        pattern.points[1].price = point_C;
        pattern.points[2].time = iTime(_Symbol, PERIOD_CURRENT, i - CP_SwingLength);
        pattern.points[2].price = iClose(_Symbol, PERIOD_CURRENT, i - CP_SwingLength);
        pattern.is_bullish = is_bullish_butterfly;
        pattern.confidence = CalculatePatternConfidence(pattern_size, 0.03);
        pattern.formation_time = TimeCurrent();
        pattern.is_valid = CheckRSIConfirmation(is_bullish_butterfly);
        pattern.obj_name = "Butterfly_" + IntegerToString(TimeCurrent());

        if(pattern.is_valid && AddPattern(pattern)) {
            if(CP_ShowPatterns) DrawButterflyPattern(g_pattern_count - 1);
            return;
        }
    }
}

//+------------------------------------------------------------------+
//| Generate Chart Pattern trading signal                           |
//+------------------------------------------------------------------+
TradingSignal GenerateChartPatternSignal() {
    TradingSignal signal;
    signal.signal_type = SIGNAL_TYPE_HOLD;
    signal.confidence_level = 0.0;
    signal.stop_loss = 0.0;
    signal.take_profit = 0.0;
    signal.parameters = "";
    signal.strategy_name = "Chart Pattern";
    signal.timestamp = TimeCurrent();
    signal.is_valid = false;

    // Find the most recent and confident pattern
    ChartPattern best_pattern;
    double best_confidence = 0.0;
    int best_index = -1;

    for(int i = 0; i < g_pattern_count; i++) {
        if(!g_detected_patterns[i].is_valid) continue;

        // Check if pattern is recent enough
        if(TimeCurrent() - g_detected_patterns[i].formation_time > 3600) continue; // 1 hour max age

        if(g_detected_patterns[i].confidence > best_confidence) {
            best_confidence = g_detected_patterns[i].confidence;
            best_pattern = g_detected_patterns[i];
            best_index = i;
        }
    }

    if(best_index >= 0 && best_confidence >= 0.6) {
        signal.signal_type = best_pattern.is_bullish ? SIGNAL_TYPE_BUY : SIGNAL_TYPE_SELL;
        signal.confidence_level = best_confidence;

        // Calculate stop loss and take profit based on pattern
        double current_price = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) + SymbolInfoDouble(_Symbol, SYMBOL_BID)) / 2;
        double atr_value = g_atr_value > 0 ? g_atr_value : 0.001;

        if(best_pattern.is_bullish) {
            signal.stop_loss = current_price - atr_value * ATR_Multiplier_SL;
            signal.take_profit = current_price + atr_value * ATR_Multiplier_TP;
        } else {
            signal.stop_loss = current_price + atr_value * ATR_Multiplier_SL;
            signal.take_profit = current_price - atr_value * ATR_Multiplier_TP;
        }

        signal.parameters = best_pattern.pattern_name + "_" + IntegerToString(best_index);
        signal.is_valid = true;
    }

    return signal;
}

//+------------------------------------------------------------------+
//| Cleanup old patterns                                            |
//+------------------------------------------------------------------+
void CleanupOldPatterns() {
    datetime current_time = TimeCurrent();

    for(int i = g_pattern_count - 1; i >= 0; i--) {
        // Remove patterns older than 4 hours
        if(current_time - g_detected_patterns[i].formation_time > 14400) {
            // Delete visual objects
            ObjectDelete(0, g_detected_patterns[i].obj_name);
            ObjectDelete(0, g_detected_patterns[i].obj_name + "_label");

            // Remove from array
            for(int j = i; j < g_pattern_count - 1; j++) {
                g_detected_patterns[j] = g_detected_patterns[j + 1];
            }
            g_pattern_count--;
        }
    }
}

//+------------------------------------------------------------------+
//| Drawing functions for patterns                                  |
//+------------------------------------------------------------------+
void DrawHeadAndShouldersPattern(int pattern_index) {
    if(pattern_index < 0 || pattern_index >= g_pattern_count) return;

    ChartPattern pattern = g_detected_patterns[pattern_index];
    color pattern_color = pattern.is_bullish ? clrBlue : clrRed;

    // Draw lines connecting the points
    string line1_name = pattern.obj_name + "_line1";
    string line2_name = pattern.obj_name + "_line2";
    string neckline_name = pattern.obj_name + "_neckline";

    // Left shoulder to head
    ObjectCreate(0, line1_name, OBJ_TREND, 0, pattern.points[0].time, pattern.points[0].price,
                 pattern.points[1].time, pattern.points[1].price);
    ObjectSetInteger(0, line1_name, OBJPROP_COLOR, pattern_color);
    ObjectSetInteger(0, line1_name, OBJPROP_WIDTH, 2);

    // Head to right shoulder
    ObjectCreate(0, line2_name, OBJ_TREND, 0, pattern.points[1].time, pattern.points[1].price,
                 pattern.points[2].time, pattern.points[2].price);
    ObjectSetInteger(0, line2_name, OBJPROP_COLOR, pattern_color);
    ObjectSetInteger(0, line2_name, OBJPROP_WIDTH, 2);

    // Neckline
    ObjectCreate(0, neckline_name, OBJ_HLINE, 0, 0, pattern.points[3].price);
    ObjectSetInteger(0, neckline_name, OBJPROP_COLOR, pattern_color);
    ObjectSetInteger(0, neckline_name, OBJPROP_STYLE, STYLE_DASH);

    // Add label
    string label_name = pattern.obj_name + "_label";
    ObjectCreate(0, label_name, OBJ_TEXT, 0, pattern.points[1].time, pattern.points[1].price);
    ObjectSetString(0, label_name, OBJPROP_TEXT, pattern.pattern_name);
    ObjectSetInteger(0, label_name, OBJPROP_COLOR, pattern_color);
    ObjectSetInteger(0, label_name, OBJPROP_FONTSIZE, 10);
}

void DrawFlagPattern(int pattern_index) {
    if(pattern_index < 0 || pattern_index >= g_pattern_count) return;

    ChartPattern pattern = g_detected_patterns[pattern_index];
    color pattern_color = pattern.is_bullish ? clrBlue : clrRed;

    // Draw flagpole
    string pole_name = pattern.obj_name + "_pole";
    ObjectCreate(0, pole_name, OBJ_TREND, 0, pattern.points[0].time, pattern.points[0].price,
                 pattern.points[1].time, pattern.points[1].price);
    ObjectSetInteger(0, pole_name, OBJPROP_COLOR, pattern_color);
    ObjectSetInteger(0, pole_name, OBJPROP_WIDTH, 3);

    // Draw flag rectangle
    string flag_name = pattern.obj_name + "_flag";
    ObjectCreate(0, flag_name, OBJ_RECTANGLE, 0, pattern.points[1].time, pattern.points[3].price,
                 pattern.points[2].time, pattern.points[1].price);
    ObjectSetInteger(0, flag_name, OBJPROP_COLOR, pattern_color);
    ObjectSetInteger(0, flag_name, OBJPROP_STYLE, STYLE_DASH);
    ObjectSetInteger(0, flag_name, OBJPROP_FILL, false);

    // Add label
    string label_name = pattern.obj_name + "_label";
    ObjectCreate(0, label_name, OBJ_TEXT, 0, pattern.points[1].time, pattern.points[1].price);
    ObjectSetString(0, label_name, OBJPROP_TEXT, pattern.pattern_name);
    ObjectSetInteger(0, label_name, OBJPROP_COLOR, pattern_color);
    ObjectSetInteger(0, label_name, OBJPROP_FONTSIZE, 10);
}

void DrawButterflyPattern(int pattern_index) {
    if(pattern_index < 0 || pattern_index >= g_pattern_count) return;

    ChartPattern pattern = g_detected_patterns[pattern_index];
    color pattern_color = pattern.is_bullish ? clrBlue : clrRed;

    // Draw connecting lines
    string line1_name = pattern.obj_name + "_line1";
    string line2_name = pattern.obj_name + "_line2";

    ObjectCreate(0, line1_name, OBJ_TREND, 0, pattern.points[0].time, pattern.points[0].price,
                 pattern.points[1].time, pattern.points[1].price);
    ObjectSetInteger(0, line1_name, OBJPROP_COLOR, pattern_color);
    ObjectSetInteger(0, line1_name, OBJPROP_WIDTH, 2);

    ObjectCreate(0, line2_name, OBJ_TREND, 0, pattern.points[1].time, pattern.points[1].price,
                 pattern.points[2].time, pattern.points[2].price);
    ObjectSetInteger(0, line2_name, OBJPROP_COLOR, pattern_color);
    ObjectSetInteger(0, line2_name, OBJPROP_WIDTH, 2);

    // Add label
    string label_name = pattern.obj_name + "_label";
    ObjectCreate(0, label_name, OBJ_TEXT, 0, pattern.points[1].time, pattern.points[1].price);
    ObjectSetString(0, label_name, OBJPROP_TEXT, pattern.pattern_name);
    ObjectSetInteger(0, label_name, OBJPROP_COLOR, pattern_color);
    ObjectSetInteger(0, label_name, OBJPROP_FONTSIZE, 10);
}

//+------------------------------------------------------------------+
//| END OF CHART PATTERN STRATEGY IMPLEMENTATION                    |
//| Integration completed: 6-strategy consensus system              |
//| Strategies: Order Block, Fair Value Gap, Market Structure,      |
//|            Range Breakout, Support/Resistance, Chart Pattern    |

