//+------------------------------------------------------------------+
//|                                                  SharedDefs.mqh |
//|         Shared definitions for the Misa<PERSON> Bot project          |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"

#include "SignalFileManager.mqh" // For TradingSignal

//--- Structs
struct StrategyConfig {
    bool   is_enabled;
    string name;
    string signal_file;
};

struct SignalStatus {
    string        strategy_name;
    TradingSignal last_signal;
    datetime      last_updated;
    bool          is_valid;
    string        status_text;
    color         status_color;
    string        signal_file; // Store the signal file for processing
};

//+------------------------------------------------------------------+
//| Get string representation of signal type                         |
//+------------------------------------------------------------------+
string GetSignalTypeString(ENUM_SIGNAL_TYPE signal_type) {
    switch (signal_type) {
        case SIGNAL_TYPE_BUY:  return "BUY";
        case SIGNAL_TYPE_SELL: return "SELL";
        case SIGNAL_TYPE_HOLD: return "HOLD";
        default:               return "UNKNOWN";
    }
}