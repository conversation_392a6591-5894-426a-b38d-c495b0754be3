//+------------------------------------------------------------------+
//|                                                  ChartPatternEA.mq5 |
//|                        Copyright 2025, MetaQuotes Software Corp.  |
//|                                             https://www.mql5.com  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Software Corp."
#property link      "https://www.mql5.com"
#property version   "2.00"
#property description "Multi-strategy trading system with chart patterns and consensus-based signal aggregation"
#property strict

#include <Trade/Trade.mqh>

//+------------------------------------------------------------------+
//| ENUMS AND STRUCTURES                                             |
//+------------------------------------------------------------------+

//--- Trading Signal Type Enum
enum ENUM_SIGNAL_TYPE {
    SIGNAL_TYPE_HOLD = 0,  // No signal
    SIGNAL_TYPE_BUY = 1,   // Buy signal
    SIGNAL_TYPE_SELL = 2   // Sell signal
};

//--- Strategy Type Enum
enum ENUM_STRATEGY_TYPE {
    STRATEGY_HEAD_AND_SHOULDERS = 0,
    STRATEGY_FLAG = 1,
    STRATEGY_BUTTERFLY = 2,
    STRATEGY_ORDER_BLOCK = 3,
    STRATEGY_FAIR_VALUE_GAP = 4,
    STRATEGY_MARKET_STRUCTURE = 5,
    STRATEGY_RANGE_BREAKOUT = 6,
    STRATEGY_SUPPORT_RESISTANCE = 7
};

//--- Trading Signal Structure
struct TradingSignal {
    ENUM_SIGNAL_TYPE signal_type;    // Type of signal (HOLD, BUY, SELL)
    double confidence_level;         // Confidence level (0.0 to 1.0)
    double stop_loss;                // Stop loss price
    double take_profit;              // Take profit price
    string parameters;               // Additional parameters
    string strategy_name;            // Name of the strategy
    datetime timestamp;              // When signal was generated
    bool is_valid;                   // Whether signal is still valid
};

//--- Order Block Structure
struct OrderBlock {
    datetime time_created;
    double high_price;
    double low_price;
    double open_price;
    double close_price;
    ENUM_TIMEFRAMES timeframe;
    bool is_bullish;
    bool is_fresh;
    bool is_broken;
    int touches;
    datetime last_touch;
    double strength;
    string obj_name;
    bool signal_sent;
    double partial_fill_ratio;
};

//--- Strategy Status Structure
struct StrategyStatus {
    string name;
    TradingSignal last_signal;
    datetime last_updated;
    bool is_active;
    color status_color;
};

//+------------------------------------------------------------------+
//| INPUT PARAMETERS                                                 |
//+------------------------------------------------------------------+

input group "=== Master Trading Settings ==="
input long MagicNumber = 789012;           // Magic number for trades
input bool EnableTrading = true;           // Master switch to enable/disable trading
input double RiskPercent = 1.0;            // Risk percentage per trade
input int MaxOpenTrades = 3;               // Maximum open trades
input double MinEquity = 100.0;           // Minimum account equity to trade

input group "=== Consensus Settings ==="
input int MinSignalConsensus = 2;          // Minimum number of signals for consensus
input double MinConfidenceThreshold = 0.65; // Minimum average confidence level
input int SignalExpirySeconds = 300;       // Signal expiry time in seconds (5 minutes)

input group "=== Chart Pattern Settings ==="
input bool EnableHeadAndShoulders = true;  // Enable Head and Shoulders
input bool EnableFlag = true;              // Enable Flag Pattern
input bool EnableButterfly = true;         // Enable Butterfly Pattern
input double PatternSensitivity = 0.1;     // Pattern sensitivity (0.05 to 0.2)
input int RSI_Period = 14;                // RSI period for momentum confirmation
input double RSI_Buy_Level = 50;          // RSI level for buy confirmation
input double RSI_Sell_Level = 50;         // RSI level for sell confirmation

input group "=== Order Block Strategy ==="
input bool EnableOrderBlock = true;        // Enable Order Block strategy
input int OB_SwingLength = 5;             // Swing detection length
input bool OB_ShowH1Blocks = true;        // Show H1 timeframe blocks
input bool OB_ShowH4Blocks = true;        // Show H4 timeframe blocks
input bool OB_ShowD1Blocks = true;        // Show D1 timeframe blocks
input double OB_MinBlockStrength = 1.0;   // Minimum block strength

input group "=== Fair Value Gap Strategy ==="
input bool EnableFairValueGap = true;      // Enable Fair Value Gap strategy
input double FVG_MinGapSize = 10.0;       // Minimum gap size in points
input double FVG_MaxMiddleCandleRatio = 0.3; // Maximum middle candle ratio

input group "=== Market Structure Strategy ==="
input bool EnableMarketStructure = true;   // Enable Market Structure strategy
input int MS_SwingPeriod = 10;            // Swing detection period

input group "=== Range Breakout Strategy ==="
input bool EnableRangeBreakout = true;     // Enable Range Breakout strategy
input int RB_RangePeriod = 24;            // Range calculation period (hours)
input int RB_ValidBreakStartHour = 6;     // Valid breakout start hour
input int RB_ValidBreakEndHour = 13;      // Valid breakout end hour

input group "=== Support/Resistance Strategy ==="
input bool EnableSupportResistance = true; // Enable Support/Resistance strategy
input int SR_LookbackPeriod = 100;        // Lookback period for S/R detection
input double SR_LevelTolerance = 10.0;    // Level tolerance in points

input group "=== Risk Management Settings ==="
input double ATR_Multiplier_SL = 2.0;     // ATR multiplier for stop-loss
input double ATR_Multiplier_TP = 4.0;     // ATR multiplier for take-profit
input double TrailingStop_ATR = 1.5;      // ATR multiplier for trailing stop

input group "=== Dashboard Settings ==="
input bool EnableDashboard = true;         // Enable/disable the visual dashboard

input group "=== Debug Settings ==="
input bool EnableDebugLogging = true;      // Enable detailed debug logging
input bool ShowSignalDetails = true;       // Show individual strategy signals in log

//+------------------------------------------------------------------+
//| GLOBAL VARIABLES                                                 |
//+------------------------------------------------------------------+

CTrade trade;
StrategyStatus g_strategies[8];
TradingSignal g_signals[8];
OrderBlock g_order_blocks[];
int g_block_count = 0;
datetime g_last_bar_time = 0;
double g_atr_value = 0;
int g_atr_handle = INVALID_HANDLE;
int rsi_handle = INVALID_HANDLE;
double point; // Point value for current symbol
int barsToScan = 100; // Number of bars to scan for patterns

// Range Breakout variables
double g_daily_high = 0;
double g_daily_low = 0;
bool g_range_established = false;
bool g_range_broken = false;

// Support/Resistance variables
double g_resistance_levels[2];
double g_support_levels[2];

// Dashboard constants
#define DASHBOARD_PREFIX "ChartPatternEA_"
#define CARD_WIDTH 220
#define CARD_HEIGHT 100
#define SPACING_X 15
#define SPACING_Y 15
#define START_X 25
#define START_Y 60

// Color definitions
#define COLOR_BACKGROUND clrBlack
#define COLOR_CARD_BG 0x2B2B2B
#define COLOR_CARD_BORDER 0x4A4A4A
#define COLOR_TEXT_HEADER clrWhite
#define COLOR_TEXT_NORMAL clrSilver
#define COLOR_TEXT_ACCENT clrDodgerBlue
#define COLOR_BUY clrSeaGreen
#define COLOR_SELL clrCrimson
#define COLOR_HOLD clrGoldenrod
#define COLOR_PROFIT clrLimeGreen
#define COLOR_LOSS clrFireBrick

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== Chart Pattern EA Initializing ===");
   
   point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   trade.SetExpertMagicNumber(MagicNumber);
   trade.SetDeviationInPoints(10);
   
   // Initialize indicators
   rsi_handle = iRSI(_Symbol, PERIOD_CURRENT, RSI_Period, PRICE_CLOSE);
   g_atr_handle = iATR(_Symbol, PERIOD_CURRENT, 14);
   if(rsi_handle == INVALID_HANDLE || g_atr_handle == INVALID_HANDLE)
   {
      Print("Failed to initialize indicators: RSI or ATR");
      return(INIT_FAILED);
   }
   
   // Initialize strategy statuses
   InitializeStrategies();
   
   // Initialize order block array
   ArrayResize(g_order_blocks, 100);
   g_block_count = 0;
   
   // Create dashboard
   if(EnableDashboard)
   {
      CreateDashboard();
   }
   
   Print("=== Chart Pattern EA Initialized Successfully ===");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   ObjectsDeleteAll(0, "Pattern_");
   ObjectsDeleteAll(0, DASHBOARD_PREFIX);
   for(int i = 0; i < g_block_count; i++)
   {
      ObjectDelete(0, g_order_blocks[i].obj_name);
      ObjectDelete(0, g_order_blocks[i].obj_name + "_label");
   }
   if(rsi_handle != INVALID_HANDLE) IndicatorRelease(rsi_handle);
   if(g_atr_handle != INVALID_HANDLE) IndicatorRelease(g_atr_handle);
   Print("=== Chart Pattern EA Deinitialized ===");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Update ATR value
   UpdateATR();
   
   // Check for new bar
   datetime current_time = iTime(_Symbol, PERIOD_CURRENT, 0);
   if(current_time != g_last_bar_time)
   {
      g_last_bar_time = current_time;
      OnNewBar();
   }
   
   // Manage trailing stops
   ManageTrailingStops();
   
   // Update dashboard
   if(EnableDashboard)
   {
      UpdateDashboard();
   }
   
   // Execute consensus trading
   if(EnableTrading)
   {
      ExecuteConsensusTrading();
   }
}

//+------------------------------------------------------------------+
//| New bar event handler                                            |
//+------------------------------------------------------------------+
void OnNewBar()
{
   if(EnableDebugLogging)
   {
      Print("=== OnNewBar() - New Bar Detected ===");
      Print("Time: ", TimeToString(TimeCurrent()));
      Print("Symbol: ", _Symbol, " Period: ", EnumToString((ENUM_TIMEFRAMES)_Period));
   }
   
   // Clear expired signals
   ClearExpiredSignals();
   
   // Run all enabled strategies
   if(EnableHeadAndShoulders)
   {
      if(EnableDebugLogging) Print("Running Head and Shoulders Strategy...");
      RunHeadAndShouldersStrategy();
   }
   if(EnableFlag)
   {
      if(EnableDebugLogging) Print("Running Flag Pattern Strategy...");
      RunFlagPatternStrategy();
   }
   if(EnableButterfly)
   {
      if(EnableDebugLogging) Print("Running Butterfly Pattern Strategy...");
      RunButterflyPatternStrategy();
   }
   if(EnableOrderBlock)
   {
      if(EnableDebugLogging) Print("Running Order Block Strategy...");
      RunOrderBlockStrategy();
   }
   if(EnableFairValueGap)
   {
      if(EnableDebugLogging) Print("Running Fair Value Gap Strategy...");
      RunFairValueGapStrategy();
   }
   if(EnableMarketStructure)
   {
      if(EnableDebugLogging) Print("Running Market Structure Strategy...");
      RunMarketStructureStrategy();
   }
   if(EnableRangeBreakout)
   {
      if(EnableDebugLogging) Print("Running Range Breakout Strategy...");
      RunRangeBreakoutStrategy();
   }
   if(EnableSupportResistance)
   {
      if(EnableDebugLogging) Print("Running Support/Resistance Strategy...");
      RunSupportResistanceStrategy();
   }
   
   if(EnableDebugLogging) Print("=== OnNewBar() Complete ===");
}

//+------------------------------------------------------------------+
//| UTILITY FUNCTIONS                                                |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Initialize strategy statuses                                     |
//+------------------------------------------------------------------+
void InitializeStrategies()
{
   g_strategies[0].name = "Head and Shoulders";
   g_strategies[1].name = "Flag Pattern";
   g_strategies[2].name = "Butterfly Pattern";
   g_strategies[3].name = "Order Block";
   g_strategies[4].name = "Fair Value Gap";
   g_strategies[5].name = "Market Structure";
   g_strategies[6].name = "Range Breakout";
   g_strategies[7].name = "Support/Resistance";
   
   for(int i = 0; i < 8; i++)
   {
      g_strategies[i].is_active = false;
      g_strategies[i].status_color = COLOR_HOLD;
      g_strategies[i].last_updated = 0;
      g_strategies[i].last_signal.signal_type = SIGNAL_TYPE_HOLD;
      g_strategies[i].last_signal.confidence_level = 0.0;
      g_strategies[i].last_signal.is_valid = false;
   }
}

//+------------------------------------------------------------------+
//| Update ATR value                                                 |
//+------------------------------------------------------------------+
void UpdateATR()
{
   if(g_atr_handle != INVALID_HANDLE)
   {
      double atr_buffer[1];
      if(CopyBuffer(g_atr_handle, 0, 1, 1, atr_buffer) > 0)
      {
         g_atr_value = atr_buffer[0];
      }
   }
}

//+------------------------------------------------------------------+
//| Check RSI for confirmation                                       |
//+------------------------------------------------------------------+
bool CheckRSI(bool isBuy)
{
   double rsi[];
   ArraySetAsSeries(rsi, true);
   if(CopyBuffer(rsi_handle, 0, 0, 1, rsi) <= 0)
   {
      Print("Failed to get RSI data");
      return false;
   }
   if(isBuy && rsi[0] >= RSI_Buy_Level) return true;
   if(!isBuy && rsi[0] <= RSI_Sell_Level) return true;
   return false;
}

//+------------------------------------------------------------------+
//| Clear expired signals                                            |
//+------------------------------------------------------------------+
void ClearExpiredSignals()
{
   datetime current_time = TimeCurrent();
   for(int i = 0; i < 8; i++)
   {
      if(g_strategies[i].last_signal.is_valid)
      {
         if(current_time - g_strategies[i].last_signal.timestamp > SignalExpirySeconds)
         {
            g_strategies[i].last_signal.is_valid = false;
            g_strategies[i].last_signal.signal_type = SIGNAL_TYPE_HOLD;
            g_strategies[i].status_color = COLOR_HOLD;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Create trading signal                                            |
//+------------------------------------------------------------------+
TradingSignal CreateTradingSignal(ENUM_SIGNAL_TYPE type, double confidence,
                                 double sl, double tp, string params, string strategy)
{
   TradingSignal signal;
   signal.signal_type = type;
   signal.confidence_level = confidence;
   signal.stop_loss = sl;
   signal.take_profit = tp;
   signal.parameters = params;
   signal.strategy_name = strategy;
   signal.timestamp = TimeCurrent();
   signal.is_valid = true;
   return signal;
}

//+------------------------------------------------------------------+
//| Update strategy signal                                           |
//+------------------------------------------------------------------+
void UpdateStrategySignal(ENUM_STRATEGY_TYPE strategy_type, TradingSignal &signal)
{
   int index = (int)strategy_type;
   if(index >= 0 && index < 8)
   {
      g_strategies[index].last_signal = signal;
      g_strategies[index].last_updated = TimeCurrent();
      g_strategies[index].is_active = signal.is_valid;
      g_strategies[index].status_color = signal.signal_type == SIGNAL_TYPE_BUY ? COLOR_BUY :
                                        signal.signal_type == SIGNAL_TYPE_SELL ? COLOR_SELL : COLOR_HOLD;
   }
}

//+------------------------------------------------------------------+
//| Manage trailing stops                                            |
//+------------------------------------------------------------------+
void ManageTrailingStops()
{
   if(g_atr_value == 0.0) return;
   
   double trailingStop = g_atr_value * TrailingStop_ATR;
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      ulong ticket = PositionGetTicket(i);
      if(PositionSelectByTicket(ticket) && PositionGetString(POSITION_SYMBOL) == _Symbol &&
         PositionGetInteger(POSITION_MAGIC) == MagicNumber)
      {
         double currentSL = PositionGetDouble(POSITION_SL);
         double currentPrice = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ?
                              SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                              SymbolInfoDouble(_Symbol, SYMBOL_ASK);
         double newSL = PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ?
                        currentPrice - trailingStop : currentPrice + trailingStop;
                        
         if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY && newSL > currentSL &&
            newSL > PositionGetDouble(POSITION_PRICE_OPEN))
         {
            MqlTradeRequest request;
            MqlTradeResult result;
            ZeroMemory(request);
            ZeroMemory(result);
            request.action = TRADE_ACTION_SLTP;
            request.position = ticket;
            request.sl = NormalizeDouble(newSL, _Digits);
            request.tp = PositionGetDouble(POSITION_TP);
            request.symbol = _Symbol;
            if(!OrderSend(request, result))
            {
               Print("Trailing stop failed for ticket ", ticket, ", error #", GetLastError());
            }
         }
         else if(PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_SELL && (currentSL == 0 || newSL < currentSL) &&
                 newSL < PositionGetDouble(POSITION_PRICE_OPEN))
         {
            MqlTradeRequest request;
            MqlTradeResult result;
            ZeroMemory(request);
            ZeroMemory(result);
            request.action = TRADE_ACTION_SLTP;
            request.position = ticket;
            request.sl = NormalizeDouble(newSL, _Digits);
            request.tp = PositionGetDouble(POSITION_TP);
            request.symbol = _Symbol;
            if(!OrderSend(request, result))
            {
               Print("Trailing stop failed for ticket ", ticket, ", error #", GetLastError());
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Execute consensus trading logic                                  |
//+------------------------------------------------------------------+
void ExecuteConsensusTrading()
{
   if(EnableDebugLogging)
   {
      Print("=== ExecuteConsensusTrading() Called ===");
      Print("EnableTrading: ", EnableTrading);
      Print("Current Positions: ", PositionsTotal(), " / Max: ", MaxOpenTrades);
      Print("Account Equity: $", AccountInfoDouble(ACCOUNT_EQUITY), " / Min Required: $", MinEquity);
   }
   
   if(!EnableTrading)
   {
      if(EnableDebugLogging) Print("TRADING DISABLED - EnableTrading = false");
      return;
   }
   if(PositionsTotal() >= MaxOpenTrades)
   {
      if(EnableDebugLogging) Print("MAX TRADES REACHED - Current: ", PositionsTotal(), " Max: ", MaxOpenTrades);
      return;
   }
   if(AccountInfoDouble(ACCOUNT_EQUITY) < MinEquity)
   {
      if(EnableDebugLogging) Print("INSUFFICIENT EQUITY - Current: $", AccountInfoDouble(ACCOUNT_EQUITY), " Required: $", MinEquity);
      return;
   }
   
   // Count valid signals by type
   int buy_signals = 0, sell_signals = 0;
   double buy_confidence_sum = 0.0, sell_confidence_sum = 0.0;
   double avg_sl_buy = 0.0, avg_tp_buy = 0.0;
   double avg_sl_sell = 0.0, avg_tp_sell = 0.0;
   
   if(EnableDebugLogging) Print("=== Analyzing Strategy Signals ===");
   
   for(int i = 0; i < 8; i++)
   {
      if(EnableDebugLogging && ShowSignalDetails)
      {
         Print("Strategy ", i, " (", g_strategies[i].name, "):");
         Print("  - Signal Valid: ", g_strategies[i].last_signal.is_valid);
         Print("  - Signal Type: ", GetSignalTypeString(g_strategies[i].last_signal.signal_type));
         Print("  - Confidence: ", g_strategies[i].last_signal.confidence_level, " (Required: ", MinConfidenceThreshold, ")");
         Print("  - Age: ", TimeCurrent() - g_strategies[i].last_signal.timestamp, " seconds");
      }
      
      if(g_strategies[i].last_signal.is_valid &&
         g_strategies[i].last_signal.confidence_level >= MinConfidenceThreshold)
      {
         if(g_strategies[i].last_signal.signal_type == SIGNAL_TYPE_BUY)
         {
            buy_signals++;
            buy_confidence_sum += g_strategies[i].last_signal.confidence_level;
            avg_sl_buy += g_strategies[i].last_signal.stop_loss;
            avg_tp_buy += g_strategies[i].last_signal.take_profit;
            if(EnableDebugLogging) Print("Valid BUY signal from ", g_strategies[i].name, " (Confidence: ", g_strategies[i].last_signal.confidence_level, ")");
         }
         else if(g_strategies[i].last_signal.signal_type == SIGNAL_TYPE_SELL)
         {
            sell_signals++;
            sell_confidence_sum += g_strategies[i].last_signal.confidence_level;
            avg_sl_sell += g_strategies[i].last_signal.stop_loss;
            avg_tp_sell += g_strategies[i].last_signal.take_profit;
            if(EnableDebugLogging) Print("Valid SELL signal from ", g_strategies[i].name, " (Confidence: ", g_strategies[i].last_signal.confidence_level, ")");
         }
      }
   }
   
   if(EnableDebugLogging)
   {
      Print("=== Signal Summary ===");
      Print("BUY Signals: ", buy_signals, " (Required: ", MinSignalConsensus, ")");
      Print("SELL Signals: ", sell_signals, " (Required: ", MinSignalConsensus, ")");
      if(buy_signals > 0) Print("Average BUY Confidence: ", buy_confidence_sum / buy_signals, " (Required: ", MinConfidenceThreshold, ")");
      if(sell_signals > 0) Print("Average SELL Confidence: ", sell_confidence_sum / sell_signals, " (Required: ", MinConfidenceThreshold, ")");
   }
   
   // Check for BUY consensus
   if(buy_signals >= MinSignalConsensus)
   {
      double avg_confidence = buy_confidence_sum / buy_signals;
      if(EnableDebugLogging) Print("BUY CONSENSUS REACHED - Signals: ", buy_signals, ", Avg Confidence: ", avg_confidence);
      if(avg_confidence >= MinConfidenceThreshold)
      {
         avg_sl_buy = buy_signals > 0 ? avg_sl_buy / buy_signals : 0;
         avg_tp_buy = buy_signals > 0 ? avg_tp_buy / buy_signals : 0;
         if(EnableDebugLogging) Print("EXECUTING BUY TRADE - Confidence: ", avg_confidence, ", SL: ", avg_sl_buy, ", TP: ", avg_tp_buy);
         ExecuteTrade(SIGNAL_TYPE_BUY, avg_confidence, avg_sl_buy, avg_tp_buy, "Consensus BUY");
         return;
      }
      else
      {
         if(EnableDebugLogging) Print("BUY CONSENSUS REJECTED - Confidence too low: ", avg_confidence, " < ", MinConfidenceThreshold);
      }
   }
   else
   {
      if(EnableDebugLogging && buy_signals > 0) Print("BUY CONSENSUS NOT REACHED - Signals: ", buy_signals, " < ", MinSignalConsensus);
   }
   
   // Check for SELL consensus
   if(sell_signals >= MinSignalConsensus)
   {
      double avg_confidence = sell_confidence_sum / sell_signals;
      if(EnableDebugLogging) Print("SELL CONSENSUS REACHED - Signals: ", sell_signals, ", Avg Confidence: ", avg_confidence);
      if(avg_confidence >= MinConfidenceThreshold)
      {
         avg_sl_sell = sell_signals > 0 ? avg_sl_sell / sell_signals : 0;
         avg_tp_sell = sell_signals > 0 ? avg_tp_sell / sell_signals : 0;
         if(EnableDebugLogging) Print("EXECUTING SELL TRADE - Confidence: ", avg_confidence, ", SL: ", avg_sl_sell, ", TP: ", avg_tp_sell);
         ExecuteTrade(SIGNAL_TYPE_SELL, avg_confidence, avg_sl_sell, avg_tp_sell, "Consensus SELL");
         return;
      }
      else
      {
         if(EnableDebugLogging) Print("SELL CONSENSUS REJECTED - Confidence too low: ", avg_confidence, " < ", MinConfidenceThreshold);
      }
   }
   else
   {
      if(EnableDebugLogging && sell_signals > 0) Print("SELL CONSENSUS NOT REACHED - Signals: ", sell_signals, " < ", MinSignalConsensus);
   }
   
   if(EnableDebugLogging && buy_signals == 0 && sell_signals == 0)
   {
      Print("NO VALID SIGNALS FOUND - All strategies inactive or below confidence threshold");
   }
}

//+------------------------------------------------------------------+
//| Execute trade based on consensus signal                         |
//+------------------------------------------------------------------+
void ExecuteTrade(ENUM_SIGNAL_TYPE signal_type, double confidence, double sl, double tp, string comment)
{
   if(EnableDebugLogging)
   {
      Print("=== ExecuteTrade() Called ===");
      Print("Signal Type: ", GetSignalTypeString(signal_type));
      Print("Confidence: ", confidence);
      Print("Stop Loss: ", sl);
      Print("Take Profit: ", tp);
      Print("Comment: ", comment);
   }
   
   double lot_size = CalculateLotSize(sl, signal_type);
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   
   if(EnableDebugLogging)
   {
      Print("Calculated Lot Size: ", lot_size);
      Print("Current Ask: ", ask);
      Print("Current Bid: ", bid);
   }
   
   bool result = false;
   
   if(signal_type == SIGNAL_TYPE_BUY)
   {
      if(sl > 0 && sl >= ask) sl = ask - g_atr_value * ATR_Multiplier_SL;
      if(tp > 0 && tp <= ask) tp = ask + g_atr_value * ATR_Multiplier_TP;
      result = trade.Buy(lot_size, _Symbol, ask, sl, tp, comment);
      if(result)
      {
         Print("BUY order executed: Lot=", lot_size, " Price=", ask, " SL=", sl, " TP=", tp, " Confidence=", confidence);
      }
   }
   else if(signal_type == SIGNAL_TYPE_SELL)
   {
      if(sl > 0 && sl <= bid) sl = bid + g_atr_value * ATR_Multiplier_SL;
      if(tp > 0 && tp >= bid) tp = bid - g_atr_value * ATR_Multiplier_TP;
      result = trade.Sell(lot_size, _Symbol, bid, sl, tp, comment);
      if(result)
      {
         Print("SELL order executed: Lot=", lot_size, " Price=", bid, " SL=", sl, " TP=", tp, " Confidence=", confidence);
      }
   }
   
   if(!result)
   {
      Print("Trade execution failed: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage                     |
//+------------------------------------------------------------------+
double CalculateLotSize(double sl, ENUM_SIGNAL_TYPE signal_type)
{
   double equity = AccountInfoDouble(ACCOUNT_EQUITY);
   double risk_amount = equity * (RiskPercent / 100.0);
   double tick_value = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
   double tick_size = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
   
   double stop_distance = g_atr_value * ATR_Multiplier_SL;
   if(signal_type == SIGNAL_TYPE_BUY)
   {
      double current_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
      if(sl > 0) stop_distance = MathAbs(current_price - sl) / point;
   }
   else if(signal_type == SIGNAL_TYPE_SELL)
   {
      double current_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
      if(sl > 0) stop_distance = MathAbs(sl - current_price) / point;
   }
   
   double lot_size = (tick_value > 0 && tick_size > 0 && stop_distance > 0) ?
                     risk_amount / (stop_distance * tick_value / tick_size) : 0.01;
                     
   double min_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
   double max_lot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
   double lot_step = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
   
   lot_size = MathMax(min_lot, MathMin(max_lot, lot_size));
   lot_size = MathFloor(lot_size / lot_step) * lot_step;
   
   return lot_size;
}

//+------------------------------------------------------------------+
//| Get signal type string                                           |
//+------------------------------------------------------------------+
string GetSignalTypeString(ENUM_SIGNAL_TYPE signal_type)
{
   switch(signal_type)
   {
      case SIGNAL_TYPE_BUY:  return "BUY";
      case SIGNAL_TYPE_SELL: return "SELL";
      case SIGNAL_TYPE_HOLD: return "HOLD";
      default:               return "UNKNOWN";
   }
}

//+------------------------------------------------------------------+
//| CHART PATTERN STRATEGIES                                         |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Run Head and Shoulders Strategy                                  |
//+------------------------------------------------------------------+
void RunHeadAndShouldersStrategy()
{
   TradingSignal signal = GenerateHeadAndShouldersSignal();
   if(signal.is_valid)
   {
      UpdateStrategySignal(STRATEGY_HEAD_AND_SHOULDERS, signal);
   }
}

//+------------------------------------------------------------------+
//| Generate Head and Shoulders Signal                               |
//+------------------------------------------------------------------+
TradingSignal GenerateHeadAndShouldersSignal()
{
   TradingSignal signal;
   signal.signal_type = SIGNAL_TYPE_HOLD;
   signal.confidence_level = 0.0;
   signal.stop_loss = 0.0;
   signal.take_profit = 0.0;
   signal.parameters = "";
   signal.strategy_name = "Head and Shoulders";
   signal.timestamp = TimeCurrent();
   signal.is_valid = false;
   
   double highs[], lows[];
   ArraySetAsSeries(highs, true);
   ArraySetAsSeries(lows, true);
   ArrayResize(highs, barsToScan);
   ArrayResize(lows, barsToScan);
   for(int i = 0; i < barsToScan; i++)
   {
      highs[i] = iHigh(_Symbol, PERIOD_CURRENT, i);
      lows[i] = iLow(_Symbol, PERIOD_CURRENT, i);
   }
   
   for(int i = 5; i < barsToScan - 5; i++)
   {
      if(highs[i] > highs[i-1] && highs[i] > highs[i+1])
      {
         double head = highs[i];
         int headIndex = i;
         double leftShoulder = 0;
         int leftShoulderIndex = 0;
         for(int j = i + 2; j < i + 5; j++)
         {
            if(highs[j] > highs[j-1] && highs[j] > highs[j+1] && highs[j] < head)
            {
               leftShoulder = highs[j];
               leftShoulderIndex = j;
               break;
            }
         }
         double rightShoulder = 0;
         int rightShoulderIndex = 0;
         for(int j = i - 2; j > i - 5; j--)
         {
            if(highs[j] > highs[j-1] && highs[j] > highs[j+1] && highs[j] < head)
            {
               rightShoulder = highs[j];
               rightShoulderIndex = j;
               break;
            }
         }
         if(leftShoulder > 0 && rightShoulder > 0)
         {
            double neckline = (lows[i+1] + lows[i-1]) / 2;
            if(MathAbs(leftShoulder - rightShoulder) < PatternSensitivity * head)
            {
               if(iClose(_Symbol, PERIOD_CURRENT, 0) < neckline && CheckRSI(false))
               {
                  signal.signal_type = SIGNAL_TYPE_SELL;
                  signal.confidence_level = 0.75;
                  signal.stop_loss = head;
                  signal.take_profit = neckline - (head - neckline) * 2;
                  signal.parameters = "Bearish_HNS_" + IntegerToString(headIndex);
                  signal.is_valid = true;
                  DrawHeadAndShoulders("Bearish", headIndex, leftShoulderIndex, rightShoulderIndex, neckline, head, leftShoulder, rightShoulder);
                  break;
               }
               else if(iClose(_Symbol, PERIOD_CURRENT, 0) > neckline && CheckRSI(true))
               {
                  signal.signal_type = SIGNAL_TYPE_BUY;
                  signal.confidence_level = 0.75;
                  signal.stop_loss = lows[i];
                  signal.take_profit = neckline + (neckline - lows[i]) * 2;
                  signal.parameters = "Bullish_HNS_" + IntegerToString(headIndex);
                  signal.is_valid = true;
                  DrawHeadAndShoulders("Bullish", headIndex, leftShoulderIndex, rightShoulderIndex, neckline, head, leftShoulder, rightShoulder);
                  break;
               }
            }
         }
      }
   }
   
   return signal;
}

//+------------------------------------------------------------------+
//| Run Flag Pattern Strategy                                        |
//+------------------------------------------------------------------+
void RunFlagPatternStrategy()
{
   TradingSignal signal = GenerateFlagPatternSignal();
   if(signal.is_valid)
   {
      UpdateStrategySignal(STRATEGY_FLAG, signal);
   }
}

//+------------------------------------------------------------------+
//| Generate Flag Pattern Signal                                     |
//+------------------------------------------------------------------+
TradingSignal GenerateFlagPatternSignal()
{
   TradingSignal signal;
   signal.signal_type = SIGNAL_TYPE_HOLD;
   signal.confidence_level = 0.0;
   signal.stop_loss = 0.0;
   signal.take_profit = 0.0;
   signal.parameters = "";
   signal.strategy_name = "Flag Pattern";
   signal.timestamp = TimeCurrent();
   signal.is_valid = false;
   
   double highs[], lows[];
   ArraySetAsSeries(highs, true);
   ArraySetAsSeries(lows, true);
   ArrayResize(highs, barsToScan);
   ArrayResize(lows, barsToScan);
   for(int i = 0; i < barsToScan; i++)
   {
      highs[i] = iHigh(_Symbol, PERIOD_CURRENT, i);
      lows[i] = iLow(_Symbol, PERIOD_CURRENT, i);
   }
   
   for(int i = 15; i < barsToScan - 5; i++)
   {
      double impulseHeight = highs[i-10] - lows[i-15];
      if(impulseHeight > PatternSensitivity * highs[i-10])
      {
         int lowIndices[3];
         double lowPrices[3];
         int count = 0;
         for(int j = i; j > i - 5 && count < 3; j--)
         {
            if(lows[j] < lows[j-1] && lows[j] < lows[j+1])
            {
               lowIndices[count] = j;
               lowPrices[count] = lows[j];
               count++;
            }
         }
         if(count == 3)
         {
            double slope = (lowPrices[0] - lowPrices[2]) / (iTime(_Symbol, PERIOD_CURRENT, lowIndices[2]) - iTime(_Symbol, PERIOD_CURRENT, lowIndices[0]));
            double flagHigh = lowPrices[0] + (highs[i] - lows[i]);
            double flagLow = lowPrices[0];
            bool inConsolidation = true;
            for(int j = i; j > i - 5; j--)
            {
               if(highs[j] > flagHigh + PatternSensitivity * impulseHeight || lows[j] < flagLow - PatternSensitivity * impulseHeight)
               {
                  inConsolidation = false;
                  break;
               }
            }
            if(inConsolidation)
            {
               double upperTrendline = lowPrices[0] + (flagHigh - flagLow);
               if(iClose(_Symbol, PERIOD_CURRENT, 0) > upperTrendline && CheckRSI(true))
               {
                  signal.signal_type = SIGNAL_TYPE_BUY;
                  signal.confidence_level = 0.7;
                  signal.stop_loss = flagLow;
                  signal.take_profit = iClose(_Symbol, PERIOD_CURRENT, 0) + g_atr_value * ATR_Multiplier_TP;
                  signal.parameters = "Bullish_Flag_" + IntegerToString(i);
                  signal.is_valid = true;
                  DrawFlagPattern("Bullish", i, i - 5, lowIndices, lowPrices, flagHigh, flagLow);
                  break;
               }
            }
         }
      }
      
      impulseHeight = highs[i-15] - lows[i-10];
      if(impulseHeight > PatternSensitivity * highs[i-15])
      {
         int highIndices[3];
         double highPrices[3];
         int count = 0;
         for(int j = i; j > i - 5 && count < 3; j--)
         {
            if(highs[j] > highs[j-1] && highs[j] > highs[j+1])
            {
               highIndices[count] = j;
               highPrices[count] = highs[j];
               count++;
            }
         }
         if(count == 3)
         {
            double slope = (highPrices[0] - highPrices[2]) / (iTime(_Symbol, PERIOD_CURRENT, highIndices[2]) - iTime(_Symbol, PERIOD_CURRENT, highIndices[0]));
            double flagHigh = highPrices[0];
            double flagLow = highPrices[0] - (highs[i] - lows[i]);
            bool inConsolidation = true;
            for(int j = i; j > i - 5; j--)
            {
               if(highs[j] > flagHigh + PatternSensitivity * impulseHeight || lows[j] < flagLow - PatternSensitivity * impulseHeight)
               {
                  inConsolidation = false;
                  break;
               }
            }
            if(inConsolidation)
            {
               double lowerTrendline = highPrices[0] - (flagHigh - flagLow);
               if(iClose(_Symbol, PERIOD_CURRENT, 0) < lowerTrendline && CheckRSI(false))
               {
                  signal.signal_type = SIGNAL_TYPE_SELL;
                  signal.confidence_level = 0.7;
                  signal.stop_loss = flagHigh;
                  signal.take_profit = iClose(_Symbol, PERIOD_CURRENT, 0) - g_atr_value * ATR_Multiplier_TP;
                  signal.parameters = "Bearish_Flag_" + IntegerToString(i);
                  signal.is_valid = true;
                  DrawFlagPattern("Bearish", i, i - 5, highIndices, highPrices, flagHigh, flagLow);
                  break;
               }
            }
         }
      }
   }
   
   return signal;
}

//+------------------------------------------------------------------+
//| Run Butterfly Pattern Strategy                                   |
//+------------------------------------------------------------------+
void RunButterflyPatternStrategy()
{
   TradingSignal signal = GenerateButterflyPatternSignal();
   if(signal.is_valid)
   {
      UpdateStrategySignal(STRATEGY_BUTTERFLY, signal);
   }
}

//+------------------------------------------------------------------+
//| Generate Butterfly Pattern Signal                                |
//+------------------------------------------------------------------+
TradingSignal GenerateButterflyPatternSignal()
{
   TradingSignal signal;
   signal.signal_type = SIGNAL_TYPE_HOLD;
   signal.confidence_level = 0.0;
   signal.stop_loss = 0.0;
   signal.take_profit = 0.0;
   signal.parameters = "";
   signal.strategy_name = "Butterfly Pattern";
   signal.timestamp = TimeCurrent();
   signal.is_valid = false;
   
   double highs[], lows[];
   ArraySetAsSeries(highs, true);
   ArraySetAsSeries(lows, true);
   ArrayResize(highs, barsToScan);
   ArrayResize(lows, barsToScan);
   for(int i = 0; i < barsToScan; i++)
   {
      highs[i] = iHigh(_Symbol, PERIOD_CURRENT, i);
      lows[i] = iLow(_Symbol, PERIOD_CURRENT, i);
   }
   
   for(int i = 10; i < barsToScan - 10; i++)
   {
      double X = lows[i+10];
      double A = highs[i+7];
      double B = lows[i+4];
      double C = highs[i+2];
      double D = iClose(_Symbol, PERIOD_CURRENT, 0);
      int X_index = i+10, A_index = i+7, B_index = i+4, C_index = i+2, D_index = 0;
      
      double XA = A - X;
      double AB = A - B;
      double BC = C - B;
      double CD = C - D;
      
      double fib127 = X + XA * 1.272;
      double fib161 = X + XA * 1.618;
      
      if(MathAbs(D - fib127) < PatternSensitivity * XA || MathAbs(D - fib161) < PatternSensitivity * XA)
      {
         if(AB / XA >= 0.618 && AB / XA <= 0.886 && BC / XA >= 0.382 && BC / XA <= 0.886 && CheckRSI(true))
         {
            signal.signal_type = SIGNAL_TYPE_BUY;
            signal.confidence_level = 0.65;
            signal.stop_loss = C - g_atr_value * ATR_Multiplier_SL;
            signal.take_profit = C + g_atr_value * ATR_Multiplier_TP;
            signal.parameters = "Bullish_Butterfly_" + IntegerToString(i);
            signal.is_valid = true;
            DrawButterflyPattern("Bullish", X_index, A_index, B_index, C_index, D_index, X, A, B, C, D);
            break;
         }
      }
      
      X = highs[i+10];
      A = lows[i+7];
      B = highs[i+4];
      C = lows[i+2];
      D = iClose(_Symbol, PERIOD_CURRENT, 0);
      X_index = i+10; A_index = i+7; B_index = i+4; C_index = i+2; D_index = 0;
      XA = X - A;
      AB = B - A;
      BC = B - C;
      CD = D - C;
      fib127 = X - XA * 1.272;
      fib161 = X - XA * 1.618;
      
      if(MathAbs(D - fib127) < PatternSensitivity * XA || MathAbs(D - fib161) < PatternSensitivity * XA)
      {
         if(AB / XA >= 0.618 && AB / XA <= 0.886 && BC / XA >= 0.382 && BC / XA <= 0.886 && CheckRSI(false))
         {
            signal.signal_type = SIGNAL_TYPE_SELL;
            signal.confidence_level = 0.65;
            signal.stop_loss = C + g_atr_value * ATR_Multiplier_SL;
            signal.take_profit = C - g_atr_value * ATR_Multiplier_TP;
            signal.parameters = "Bearish_Butterfly_" + IntegerToString(i);
            signal.is_valid = true;
            DrawButterflyPattern("Bearish", X_index, A_index, B_index, C_index, D_index, X, A, B, C, D);
            break;
         }
      }
   }
   
   return signal;
}

//+------------------------------------------------------------------+
//| Draw Head and Shoulders Pattern                                  |
//+------------------------------------------------------------------+
void DrawHeadAndShoulders(string type, int headIndex, int leftShoulderIndex, int rightShoulderIndex,
                          double neckline, double head, double leftShoulder, double rightShoulder)
{
   string objName = "Pattern_HeadAndShoulders_" + type + "_" + TimeToString(iTime(_Symbol, PERIOD_CURRENT, headIndex));
   
   ObjectCreate(0, objName + "_Label", OBJ_TEXT, 0, iTime(_Symbol, PERIOD_CURRENT, headIndex), head + 10 * point);
   ObjectSetString(0, objName + "_Label", OBJPROP_TEXT, "Head and Shoulders (" + type + ")");
   ObjectSetInteger(0, objName + "_Label", OBJPROP_COLOR, clrYellow);
   ObjectSetInteger(0, objName + "_Label", OBJPROP_FONTSIZE, 10);
   
   ObjectCreate(0, objName + "_Neckline", OBJ_HLINE, 0, iTime(_Symbol, PERIOD_CURRENT, headIndex), neckline);
   ObjectSetInteger(0, objName + "_Neckline", OBJPROP_COLOR, clrBlue);
   ObjectSetInteger(0, objName + "_Neckline", OBJPROP_STYLE, STYLE_SOLID);
   
   double topPrice = MathMax(head, MathMax(leftShoulder, rightShoulder));
   double bottomPrice = neckline;
   if(type == "Bullish")
   {
      topPrice = neckline;
      bottomPrice = MathMin(head, MathMin(leftShoulder, rightShoulder));
   }
   ObjectCreate(0, objName + "_Fill", OBJ_RECTANGLE, 0, iTime(_Symbol, PERIOD_CURRENT, leftShoulderIndex), topPrice,
                iTime(_Symbol, PERIOD_CURRENT, rightShoulderIndex), bottomPrice);
   ObjectSetInteger(0, objName + "_Fill", OBJPROP_COLOR, type == "Bullish" ? clrBlue : clrRed);
   ObjectSetInteger(0, objName + "_Fill", OBJPROP_FILL, true);
   ObjectSetInteger(0, objName + "_Fill", OBJPROP_BACK, true);
   ObjectSetInteger(0, objName + "_Fill", OBJPROP_STYLE, STYLE_DOT);
   
   ObjectCreate(0, objName + "_Head", OBJ_TEXT, 0, iTime(_Symbol, PERIOD_CURRENT, headIndex), head);
   ObjectSetString(0, objName + "_Head", OBJPROP_TEXT, "Head");
   ObjectSetInteger(0, objName + "_Head", OBJPROP_COLOR, clrWhite);
   
   ObjectCreate(0, objName + "_LeftShoulder", OBJ_TEXT, 0, iTime(_Symbol, PERIOD_CURRENT, leftShoulderIndex), leftShoulder);
   ObjectSetString(0, objName + "_LeftShoulder", OBJPROP_TEXT, "Left Shoulder");
   ObjectSetInteger(0, objName + "_LeftShoulder", OBJPROP_COLOR, clrWhite);
   
   ObjectCreate(0, objName + "_RightShoulder", OBJ_TEXT, 0, iTime(_Symbol, PERIOD_CURRENT, rightShoulderIndex), rightShoulder);
   ObjectSetString(0, objName + "_RightShoulder", OBJPROP_TEXT, "Right Shoulder");
   ObjectSetInteger(0, objName + "_RightShoulder", OBJPROP_COLOR, clrWhite);
}

//+------------------------------------------------------------------+
//| Draw Flag Pattern                                                |
//+------------------------------------------------------------------+
void DrawFlagPattern(string type, int flagStart, int flagEnd, int &indices[], double &prices[],
                     double flagHigh, double flagLow)
{
   string objName = "Pattern_Flag_" + type + "_" + TimeToString(iTime(_Symbol, PERIOD_CURRENT, flagStart));
   
   ObjectCreate(0, objName + "_Label", OBJ_TEXT, 0, iTime(_Symbol, PERIOD_CURRENT, flagEnd), flagHigh + 10 * point);
   ObjectSetString(0, objName + "_Label", OBJPROP_TEXT, "Flag (" + type + ")");
   ObjectSetInteger(0, objName + "_Label", OBJPROP_COLOR, clrYellow);
   ObjectSetInteger(0, objName + "_Label", OBJPROP_FONTSIZE, 10);
   
   string coreName = type == "Bullish" ? "_CoreLow" : "_CoreHigh";
   ObjectCreate(0, objName + coreName, OBJ_TREND, 0, iTime(_Symbol, PERIOD_CURRENT, indices[2]), prices[2],
                iTime(_Symbol, PERIOD_CURRENT, indices[0]), prices[0]);
   ObjectSetInteger(0, objName + coreName, OBJPROP_COLOR, clrGreen);
   ObjectSetInteger(0, objName + coreName, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, objName + coreName, OBJPROP_WIDTH, 2);
   
   double offset = flagHigh - flagLow;
   double upperPriceStart = type == "Bullish" ? prices[2] + offset : prices[2] - offset;
   double upperPriceEnd = type == "Bullish" ? prices[0] + offset : prices[0] - offset;
   ObjectCreate(0, objName + "_ParallelUpper", OBJ_TREND, 0, iTime(_Symbol, PERIOD_CURRENT, indices[2]), upperPriceStart,
                iTime(_Symbol, PERIOD_CURRENT, indices[0]), upperPriceEnd);
   ObjectSetInteger(0, objName + "_ParallelUpper", OBJPROP_COLOR, clrGreen);
   ObjectSetInteger(0, objName + "_ParallelUpper", OBJPROP_STYLE, STYLE_DOT);
   
   double lowerPriceStart = type == "Bullish" ? prices[2] - offset : prices[2] + offset;
   double lowerPriceEnd = type == "Bullish" ? prices[0] - offset : prices[0] + offset;
   ObjectCreate(0, objName + "_ParallelLower", OBJ_TREND, 0, iTime(_Symbol, PERIOD_CURRENT, indices[2]), lowerPriceStart,
                iTime(_Symbol, PERIOD_CURRENT, indices[0]), lowerPriceEnd);
   ObjectSetInteger(0, objName + "_ParallelLower", OBJPROP_COLOR, clrGreen);
   ObjectSetInteger(0, objName + "_ParallelLower", OBJPROP_STYLE, STYLE_DOT);
   
   ObjectCreate(0, objName + "_Rectangle", OBJ_RECTANGLE, 0, iTime(_Symbol, PERIOD_CURRENT, flagStart), flagHigh,
                iTime(_Symbol, PERIOD_CURRENT, flagEnd), flagLow);
   ObjectSetInteger(0, objName + "_Rectangle", OBJPROP_COLOR, type == "Bullish" ? clrBlue : clrRed);
   ObjectSetInteger(0, objName + "_Rectangle", OBJPROP_FILL, true);
   ObjectSetInteger(0, objName + "_Rectangle", OBJPROP_BACK, true);
   ObjectSetInteger(0, objName + "_Rectangle", OBJPROP_STYLE, STYLE_DOT);
}

//+------------------------------------------------------------------+
//| Draw Butterfly Pattern                                           |
//+------------------------------------------------------------------+
void DrawButterflyPattern(string type, int X_index, int A_index, int B_index, int C_index, int D_index,
                          double X, double A, double B, double C, double D)
{
   string objName = "Pattern_Butterfly_" + type + "_" + TimeToString(iTime(_Symbol, PERIOD_CURRENT, X_index));
   
   ObjectCreate(0, objName + "_Label", OBJ_TEXT, 0, iTime(_Symbol, PERIOD_CURRENT, D_index), D + 10 * point);
   ObjectSetString(0, objName + "_Label", OBJPROP_TEXT, "Butterfly (" + type + ")");
   ObjectSetInteger(0, objName + "_Label", OBJPROP_COLOR, clrYellow);
   ObjectSetInteger(0, objName + "_Label", OBJPROP_FONTSIZE, 10);
   
   ObjectCreate(0, objName + "_XA", OBJ_TREND, 0, iTime(_Symbol, PERIOD_CURRENT, X_index), X,
                iTime(_Symbol, PERIOD_CURRENT, A_index), A);
   ObjectCreate(0, objName + "_AB", OBJ_TREND, 0, iTime(_Symbol, PERIOD_CURRENT, A_index), A,
                iTime(_Symbol, PERIOD_CURRENT, B_index), B);
   ObjectCreate(0, objName + "_BC", OBJ_TREND, 0, iTime(_Symbol, PERIOD_CURRENT, B_index), B,
                iTime(_Symbol, PERIOD_CURRENT, C_index), C);
   ObjectCreate(0, objName + "_CD", OBJ_TREND, 0, iTime(_Symbol, PERIOD_CURRENT, C_index), C,
                iTime(_Symbol, PERIOD_CURRENT, D_index), D);
   ObjectSetInteger(0, objName + "_XA", OBJPROP_COLOR, clrPurple);
   ObjectSetInteger(0, objName + "_AB", OBJPROP_COLOR, clrPurple);
   ObjectSetInteger(0, objName + "_BC", OBJPROP_COLOR, clrPurple);
   ObjectSetInteger(0, objName + "_CD", OBJPROP_COLOR, clrPurple);
   
   double topPrice = MathMax(X, MathMax(A, MathMax(B, MathMax(C, D))));
   double bottomPrice = MathMin(X, MathMin(A, MathMin(B, MathMin(C, D))));
   ObjectCreate(0, objName + "_Fill", OBJ_RECTANGLE, 0, iTime(_Symbol, PERIOD_CURRENT, X_index), topPrice,
                iTime(_Symbol, PERIOD_CURRENT, D_index), bottomPrice);
   ObjectSetInteger(0, objName + "_Fill", OBJPROP_COLOR, type == "Bullish" ? clrBlue : clrRed);
   ObjectSetInteger(0, objName + "_Fill", OBJPROP_FILL, true);
   ObjectSetInteger(0, objName + "_Fill", OBJPROP_BACK, true);
   ObjectSetInteger(0, objName + "_Fill", OBJPROP_STYLE, STYLE_DOT);
   
   ObjectCreate(0, objName + "_X", OBJ_TEXT, 0, iTime(_Symbol, PERIOD_CURRENT, X_index), X);
   ObjectSetString(0, objName + "_X", OBJPROP_TEXT, "X");
   ObjectSetInteger(0, objName + "_X", OBJPROP_COLOR, clrWhite);
   
   ObjectCreate(0, objName + "_A", OBJ_TEXT, 0, iTime(_Symbol, PERIOD_CURRENT, A_index), A);
   ObjectSetString(0, objName + "_A", OBJPROP_TEXT, "A");
   ObjectSetInteger(0, objName + "_A", OBJPROP_COLOR, clrWhite);
   
   ObjectCreate(0, objName + "_B", OBJ_TEXT, 0, iTime(_Symbol, PERIOD_CURRENT, B_index), B);
   ObjectSetString(0, objName + "_B", OBJPROP_TEXT, "B");
   ObjectSetInteger(0, objName + "_B", OBJPROP_COLOR, clrWhite);
   
   ObjectCreate(0, objName + "_C", OBJ_TEXT, 0, iTime(_Symbol, PERIOD_CURRENT, C_index), C);
   ObjectSetString(0, objName + "_C", OBJPROP_TEXT, "C");
   ObjectSetInteger(0, objName + "_C", OBJPROP_COLOR, clrWhite);
   
   ObjectCreate(0, objName + "_D", OBJ_TEXT, 0, iTime(_Symbol, PERIOD_CURRENT, D_index), D);
   ObjectSetString(0, objName + "_D", OBJPROP_TEXT, "D");
   ObjectSetInteger(0, objName + "_D", OBJPROP_COLOR, clrWhite);
}

//+------------------------------------------------------------------+
//| ORDER BLOCK STRATEGY IMPLEMENTATION                             |
//+------------------------------------------------------------------+

void RunOrderBlockStrategy()
{
   if(OB_ShowH1Blocks && PERIOD_CURRENT < PERIOD_H1) DetectOrderBlocks(PERIOD_H1);
   if(OB_ShowH4Blocks && PERIOD_CURRENT < PERIOD_H4) DetectOrderBlocks(PERIOD_H4);
   if(OB_ShowD1Blocks && PERIOD_CURRENT < PERIOD_D1) DetectOrderBlocks(PERIOD_D1);
   UpdateOrderBlocks();
   TradingSignal signal = GenerateOrderBlockSignal();
   if(signal.is_valid) UpdateStrategySignal(STRATEGY_ORDER_BLOCK, signal);
   CleanupOrderBlocks();
}

void DetectOrderBlocks(ENUM_TIMEFRAMES tf)
{
   int bars_count = MathMin(500, iBars(_Symbol, tf));
   if(bars_count < OB_SwingLength * 2) return;
   
   double high[], low[], open[], close[];
   long volume[];
   datetime time[];
   ArraySetAsSeries(high, true);
   ArraySetAsSeries(low, true);
   ArraySetAsSeries(open, true);
   ArraySetAsSeries(close, true);
   ArraySetAsSeries(volume, true);
   ArraySetAsSeries(time, true);
   
   if(CopyHigh(_Symbol, tf, 0, bars_count, high) <= 0) return;
   if(CopyLow(_Symbol, tf, 0, bars_count, low) <= 0) return;
   if(CopyOpen(_Symbol, tf, 0, bars_count, open) <= 0) return;
   if(CopyClose(_Symbol, tf, 0, bars_count, close) <= 0) return;
   if(CopyTickVolume(_Symbol, tf, 0, bars_count, volume) <= 0) return;
   if(CopyTime(_Symbol, tf, 0, bars_count, time) <= 0) return;
   
   for(int i = OB_SwingLength; i < bars_count - OB_SwingLength - 1; i++)
   {
      if(IsSwingHigh(high, i, OB_SwingLength))
      {
         int ob_index = FindOrderBlockCandle(open, close, high, low, volume, i, true, tf);
         if(ob_index > 0)
         {
            CreateOrderBlock(time[ob_index], high[ob_index], low[ob_index], tf, true, open[ob_index], close[ob_index]);
         }
      }
      if(IsSwingLow(low, i, OB_SwingLength))
      {
         int ob_index = FindOrderBlockCandle(open, close, high, low, volume, i, false, tf);
         if(ob_index > 0)
         {
            CreateOrderBlock(time[ob_index], high[ob_index], low[ob_index], tf, false, open[ob_index], close[ob_index]);
         }
      }
   }
}

bool IsSwingHigh(const double &high[], int index, int swing_length)
{
   double current_high = high[index];
   for(int i = 1; i <= swing_length; i++)
   {
      if(high[index + i] >= current_high || high[index - i] >= current_high) return false;
   }
   return true;
}

bool IsSwingLow(const double &low[], int index, int swing_length)
{
   double current_low = low[index];
   for(int i = 1; i <= swing_length; i++)
   {
      if(low[index + i] <= current_low || low[index - i] <= current_low) return false;
   }
   return true;
}

int FindOrderBlockCandle(const double &open[], const double &close[], const double &high[],
                         const double &low[], const long &volume[], int swing_index, bool is_bullish, ENUM_TIMEFRAMES tf)
{
   int search_range = 10;
   int best_index = -1;
   long best_volume = 0;
   
   for(int i = swing_index + 1; i <= swing_index + search_range && i < ArraySize(open); i++)
   {
      bool is_valid_block = (is_bullish && close[i] < open[i] && volume[i] > best_volume) ||
                            (!is_bullish && close[i] > open[i] && volume[i] > best_volume);
      if(is_valid_block)
      {
         best_index = i;
         best_volume = volume[i];
      }
   }
   return best_index;
}

void CreateOrderBlock(datetime time, double high, double low, ENUM_TIMEFRAMES tf,
                      bool is_bullish, double open_price, double close_price)
{
   for(int i = 0; i < g_block_count; i++)
   {
      if(MathAbs(g_order_blocks[i].high_price - high) < point &&
         MathAbs(g_order_blocks[i].low_price - low) < point &&
         g_order_blocks[i].timeframe == tf) return;
   }
   
   if(g_block_count >= ArraySize(g_order_blocks)) ArrayResize(g_order_blocks, g_block_count + 50);
   
   OrderBlock new_block;
   new_block.time_created = time;
   new_block.high_price = high;
   new_block.low_price = low;
   new_block.open_price = open_price;
   new_block.close_price = close_price;
   new_block.timeframe = tf;
   new_block.is_bullish = is_bullish;
   new_block.is_fresh = true;
   new_block.is_broken = false;
   new_block.touches = 0;
   new_block.last_touch = 0;
   new_block.strength = CalculateBlockStrength(high, low, open_price, close_price, tf);
   new_block.obj_name = "OB_" + IntegerToString(time) + "_" + EnumToString(tf);
   new_block.signal_sent = false;
   new_block.partial_fill_ratio = 0.0;
   
   g_order_blocks[g_block_count] = new_block;
   g_block_count++;
   CreateBlockVisual(g_block_count - 1);
}

double CalculateBlockStrength(double high, double low, double open_price, double close_price, ENUM_TIMEFRAMES tf)
{
   double strength = 1.0;
   double candle_size = high - low;
   if(g_atr_value > 0) strength += (candle_size / g_atr_value) * 0.5;
   switch(tf)
   {
      case PERIOD_D1: strength += 3.0; break;
      case PERIOD_H4: strength += 2.0; break;
      case PERIOD_H1: strength += 1.0; break;
      default: strength += 0.5; break;
   }
   double body_size = MathAbs(close_price - open_price);
   if(body_size > candle_size * 0.7) strength += 0.5;
   return strength;
}

void CreateBlockVisual(int block_index)
{
   if(block_index < 0 || block_index >= g_block_count) return;
   
   OrderBlock block = g_order_blocks[block_index];
   color block_color = block.is_bullish ? clrBlue : clrRed;
   
   ObjectCreate(0, block.obj_name, OBJ_RECTANGLE, 0, block.time_created, block.high_price,
                TimeCurrent() + PeriodSeconds() * 100, block.low_price);
   ObjectSetInteger(0, block.obj_name, OBJPROP_COLOR, block_color);
   ObjectSetInteger(0, block.obj_name, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, block.obj_name, OBJPROP_WIDTH, 1);
   ObjectSetInteger(0, block.obj_name, OBJPROP_FILL, true);
   ObjectSetInteger(0, block.obj_name, OBJPROP_BACK, true);
   ObjectSetInteger(0, block.obj_name, OBJPROP_SELECTABLE, false);
   
   string label_text = (block.is_bullish ? "Bull OB " : "Bear OB ") + EnumToString(block.timeframe) + " (" +
                       DoubleToString(block.strength, 1) + ")";
   ObjectCreate(0, block.obj_name + "_label", OBJ_TEXT, 0, block.time_created,
                block.is_bullish ? block.low_price : block.high_price);
   ObjectSetString(0, block.obj_name + "_label", OBJPROP_TEXT, label_text);
   ObjectSetInteger(0, block.obj_name + "_label", OBJPROP_COLOR, block_color);
   ObjectSetInteger(0, block.obj_name + "_label", OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(0, block.obj_name + "_label", OBJPROP_SELECTABLE, false);
}

void UpdateOrderBlocks()
{
   double current_high = iHigh(_Symbol, PERIOD_CURRENT, 0);
   double current_low = iLow(_Symbol, PERIOD_CURRENT, 0);
   double current_close = iClose(_Symbol, PERIOD_CURRENT, 0);
   
   for(int i = 0; i < g_block_count; i++)
   {
      if(g_order_blocks[i].is_broken) continue;
      
      bool is_touching = (g_order_blocks[i].is_bullish &&
                         current_low <= g_order_blocks[i].high_price &&
                         current_low >= g_order_blocks[i].low_price) ||
                        (!g_order_blocks[i].is_bullish &&
                         current_high >= g_order_blocks[i].low_price &&
                         current_high <= g_order_blocks[i].high_price);
      
      if(is_touching)
      {
         g_order_blocks[i].touches++;
         g_order_blocks[i].last_touch = TimeCurrent();
         CheckBlockBreak(i, current_high, current_low);
      }
      UpdateBlockVisual(i);
   }
}

void CheckBlockBreak(int block_index, double current_high, double current_low)
{
   if(block_index < 0 || block_index >= g_block_count || g_order_blocks[block_index].is_broken) return;
   
   bool is_broken = g_order_blocks[block_index].is_bullish ?
                    (current_low < g_order_blocks[block_index].low_price) :
                    (current_high > g_order_blocks[block_index].high_price);
                    
   if(is_broken)
   {
      g_order_blocks[block_index].is_broken = true;
      g_order_blocks[block_index].is_fresh = false;
      ObjectSetInteger(0, g_order_blocks[block_index].obj_name, OBJPROP_STYLE, STYLE_DOT);
      ObjectSetInteger(0, g_order_blocks[block_index].obj_name, OBJPROP_COLOR, clrGray);
   }
}

void UpdateBlockVisual(int block_index)
{
   if(block_index < 0 || block_index >= g_block_count) return;
   
   OrderBlock block = g_order_blocks[block_index];
   ObjectSetInteger(0, block.obj_name, OBJPROP_TIME, 1, TimeCurrent() + PeriodSeconds() * 100);
}

TradingSignal GenerateOrderBlockSignal()
{
   TradingSignal signal;
   signal.signal_type = SIGNAL_TYPE_HOLD;
   signal.confidence_level = 0.0;
   signal.stop_loss = 0.0;
   signal.take_profit = 0.0;
   signal.parameters = "";
   signal.strategy_name = "Order Block";
   signal.timestamp = TimeCurrent();
   signal.is_valid = false;
   
   int best_block = -1;
   double best_strength = 0.0;
   
   for(int i = 0; i < g_block_count; i++)
   {
      OrderBlock block = g_order_blocks[i];
      if(block.is_fresh && !block.is_broken && !block.signal_sent &&
         block.strength > best_strength && block.strength >= OB_MinBlockStrength)
      {
         best_block = i;
         best_strength = block.strength;
      }
   }
   
   if(best_block >= 0)
   {
      OrderBlock block = g_order_blocks[best_block];
      double current_price = (SymbolInfoDouble(_Symbol, SYMBOL_ASK) + SymbolInfoDouble(_Symbol, SYMBOL_BID)) / 2;
      double distance_to_block = block.is_bullish ? MathAbs(current_price - block.low_price) :
                                                   MathAbs(current_price - block.high_price);
      
      if(distance_to_block <= g_atr_value * 0.5 && CheckRSI(block.is_bullish))
      {
         signal.signal_type = block.is_bullish ? SIGNAL_TYPE_BUY : SIGNAL_TYPE_SELL;
         signal.confidence_level = MathMin(0.9, block.strength / 5.0);
         signal.stop_loss = block.is_bullish ? block.low_price - g_atr_value * ATR_Multiplier_SL :
                                              block.high_price + g_atr_value * ATR_Multiplier_SL;
         signal.take_profit = block.is_bullish ? block.high_price + (block.high_price - block.low_price) * 2 :
                                                block.low_price - (block.high_price - block.low_price) * 2;
         signal.parameters = "Block_" + IntegerToString(best_block) + "_" + EnumToString(block.timeframe);
         signal.is_valid = true;
         g_order_blocks[best_block].signal_sent = true;
      }
   }
   
   return signal;
}

void CleanupOrderBlocks()
{
   datetime current_time = TimeCurrent();
   for(int i = g_block_count - 1; i >= 0; i--)
   {
      bool should_remove = (current_time - g_order_blocks[i].time_created > 86400) ||
                          (g_order_blocks[i].is_broken && current_time - g_order_blocks[i].last_touch > 3600) ||
                          (g_order_blocks[i].touches > 5);
      
      if(should_remove)
      {
         ObjectDelete(0, g_order_blocks[i].obj_name);
         ObjectDelete(0, g_order_blocks[i].obj_name + "_label");
         for(int j = i; j < g_block_count - 1; j++)
         {
            g_order_blocks[j] = g_order_blocks[j + 1];
         }
         g_block_count--;
         ArrayResize(g_order_blocks, g_block_count);
      }
   }
}

//+------------------------------------------------------------------+
//| FAIR VALUE GAP STRATEGY IMPLEMENTATION                          |
//+------------------------------------------------------------------+

void RunFairValueGapStrategy()
{
   TradingSignal signal = GenerateFairValueGapSignal();
   if(signal.is_valid) UpdateStrategySignal(STRATEGY_FAIR_VALUE_GAP, signal);
}

TradingSignal GenerateFairValueGapSignal()
{
   TradingSignal signal;
   signal.signal_type = SIGNAL_TYPE_HOLD;
   signal.confidence_level = 0.0;
   signal.stop_loss = 0.0;
   signal.take_profit = 0.0;
   signal.parameters = "";
   signal.strategy_name = "Fair Value Gap";
   signal.timestamp = TimeCurrent();
   signal.is_valid = false;
   
   for(int i = 1; i <= 10; i++)
   {
      double high = iHigh(_Symbol, PERIOD_CURRENT, i);
      double low = iLow(_Symbol, PERIOD_CURRENT, i);
      double open = iOpen(_Symbol, PERIOD_CURRENT, i);
      double close = iClose(_Symbol, PERIOD_CURRENT, i);
      
      if(i > 0)
      {
         double prevHigh = iHigh(_Symbol, PERIOD_CURRENT, i+1);
         double prevLow = iLow(_Symbol, PERIOD_CURRENT, i+1);
         
         if(low > prevHigh)
         {
            double gap_size = (low - prevHigh) / point;
            if(gap_size >= FVG_MinGapSize && CheckRSI(true))
            {
               signal.signal_type = SIGNAL_TYPE_BUY;
               signal.confidence_level = 0.7;
               signal.stop_loss = low - (high - low) * 0.5;
               signal.take_profit = high + (high - low) * 2;
               signal.parameters = "FVG_Bullish_" + IntegerToString(i);
               signal.is_valid = true;
               break;
            }
         }
         else if(high < prevLow)
         {
            double gap_size = (prevLow - high) / point;
            if(gap_size >= FVG_MinGapSize && CheckRSI(false))
            {
               signal.signal_type = SIGNAL_TYPE_SELL;
               signal.confidence_level = 0.7;
               signal.stop_loss = high + (high - low) * 0.5;
               signal.take_profit = low - (high - low) * 2;
               signal.parameters = "FVG_Bearish_" + IntegerToString(i);
               signal.is_valid = true;
               break;
            }
         }
      }
      
      if(i >= 2 && CheckMorningStarPattern(i))
      {
         signal.signal_type = SIGNAL_TYPE_BUY;
         signal.confidence_level = 0.6;
         signal.stop_loss = iLow(_Symbol, PERIOD_CURRENT, i) - g_atr_value * ATR_Multiplier_SL;
         signal.take_profit = iHigh(_Symbol, PERIOD_CURRENT, i) + g_atr_value * ATR_Multiplier_TP;
         signal.parameters = "MorningStar_" + IntegerToString(i);
         signal.is_valid = true;
         break;
      }
      
      if(CheckHammerPattern(i))
      {
         signal.signal_type = SIGNAL_TYPE_BUY;
         signal.confidence_level = 0.65;
         signal.stop_loss = iLow(_Symbol, PERIOD_CURRENT, i) - g_atr_value * ATR_Multiplier_SL